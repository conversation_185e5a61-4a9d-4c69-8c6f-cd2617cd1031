name: Build Docker Image on PR to Dev

on:
  pull_request:
    branches:
      - dev

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and push Docker images
        uses: docker/build-push-action@v5.3.0
        with:
          context: .
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Log Docker image size
        run: |
          IMAGE_SIZE=$(docker images local-image --format "{{.Size}}")
          echo "Docker image size: $IMAGE_SIZE"

import { PropsWithChildren, useEffect } from "react";
import {
  toggleAnimation,
  toggleLayout,
  toggleLocale,
  toggleMenu,
  toggleNavbar,
  toggleRTL,
  toggleSemidark,
  toggleTheme,
} from "./store/themeConfigSlice";
import { useDispatch, useSelector } from "react-redux";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

import { IRootState } from "./store";
import { useTranslation } from "react-i18next";

function App({ children }: PropsWithChildren) {
  const themeConfig = useSelector((state: IRootState) => state.themeConfig);
  const dispatch = useDispatch();
  const { i18n } = useTranslation();

  useEffect(() => {
    dispatch(toggleTheme(localStorage.getItem("theme") || themeConfig.theme));
    dispatch(toggleMenu(localStorage.getItem("menu") || themeConfig.menu));
    dispatch(
      toggleLayout(localStorage.getItem("layout") || themeConfig.layout)
    );
    dispatch(
      toggleRTL(localStorage.getItem("rtlClass") || themeConfig.rtlClass)
    );
    dispatch(
      toggleAnimation(
        localStorage.getItem("animation") || themeConfig.animation
      )
    );
    dispatch(
      toggleNavbar(localStorage.getItem("navbar") || themeConfig.navbar)
    );
    dispatch(
      toggleSemidark(localStorage.getItem("semidark") || themeConfig.semidark)
    );
    // locale
    const locale = localStorage.getItem("i18nextLng") || themeConfig.locale;
    dispatch(toggleLocale(locale));
    i18n.changeLanguage(locale);
  }, [
    dispatch,
    themeConfig.theme,
    themeConfig.menu,
    themeConfig.layout,
    themeConfig.rtlClass,
    themeConfig.animation,
    themeConfig.navbar,
    themeConfig.locale,
    themeConfig.semidark,
  ]);

  return (
    <>
      <div
        className={`${(themeConfig.sidebar && "toggle-sidebar") || ""} ${
          themeConfig.menu
        } ${themeConfig.layout} ${
          themeConfig.rtlClass
        } main-section font-nunito relative text-sm font-normal antialiased`}
      >
        {children}
      </div>
      <ToastContainer />
    </>
  );
}

export default App;

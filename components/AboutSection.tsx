import { IUser } from "@/interface";
import React from "react";
import EducationCard from "./EducationCard";
import brainIcon from "@/public/assets/svg/brainIcon.svg";
import Image from "next/image";

const AboutSection: React.FC<{
  data: IUser;
}> = ({ data }) => {
  return (
    <>
      <section
        id="about"
        className="my-8 space-y-4 rounded-lg border border-gray-100 bg-white px-8 py-6 shadow-lg"
      >
        <h1 className="text-3xl font-medium text-gray-900">
          About {data.firstName.charAt(0).toUpperCase() + "."} {data.lastName.charAt(0).toUpperCase() + "."}
        </h1>
        <p className="text-lg text-gray-600">{data.summary}</p>
      </section>

      {data?.techStack?.length ? (
        <section
          id="education"
          className="my-8 space-y-4 rounded-lg border border-gray-100 bg-white px-8 py-6 shadow-lg"
        >
          <h1 className="text-3xl font-medium text-gray-900">Skills</h1>

          <div className="flex flex-wrap gap-3">
            {data.techStack.map((skill) => (
              <div
                key={skill}
                className="flex items-center gap-2 rounded-full border border-gray-200 bg-white px-4 py-2"
              >
                <Image src={brainIcon} alt="brainIcon" />
                <span className="text-sm font-medium">{skill}</span>
              </div>
            ))}
          </div>
        </section>
      ): null}

      {data?.educationDetails?.length ? (
        <section
          id="education"
          className="my-8 space-y-4 rounded-lg border border-gray-100 bg-white px-8 py-6 shadow-lg"
        >
          <h1 className="text-3xl font-medium text-gray-900">Education</h1>

          <div className="space-y-4">
            {data.educationDetails.map((education) => (
              <EducationCard
                key={education.id}
                course={education.course}
                university={education.university}
                department={education.department}
                startDate={education.startDate}
                endDate={education.endDate}
              />
            ))}
          </div>
        </section>
      ): null}

    </>
  );
};

export default AboutSection;

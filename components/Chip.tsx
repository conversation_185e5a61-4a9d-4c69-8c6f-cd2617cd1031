import React from "react";

interface Props {
  children: React.ReactNode;
  handleClick?: () => void;
}

const Chip: React.FC<Props> = ({ children, handleClick }) => {
  return (
    <div className="relative whitespace-nowrap rounded-full border border-[#8D3F42] px-2 py-0.5 text-center text-xs text-black dark:shadow">
      {children}
      <button
        type="button"
        className="absolute -right-1 -top-1 rounded-full bg-[#8D3F42] p-0.5 text-white"
        title="Clear Filters"
        onClick={(event) => {
          event.stopPropagation();
          handleClick && handleClick();
        }}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          strokeWidth="1.5"
          stroke="currentColor"
          className="h-2 w-2 text-white"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
      </button>
    </div>
  );
};

export default Chip;

import React from "react";
import Image from "next/image";
import collegeIcon from "@/public/assets/svg/collegeIcon.svg";
import moment from "moment";

export interface IEducationCardProps {
  course: string;
  university: string;
  department: string;
  startDate: string;
  endDate: string;
}

const EducationCard: React.FC<IEducationCardProps> = ({
  course,
  department,
  endDate,
  startDate,
  university,
}) => {
  return (
    <div className="flex items-center gap-5 rounded-lg border p-6">
      <div>
        <Image src={collegeIcon} alt="collegeIcon" />
      </div>

      <div className="space-y-3">
        <h1 className="space-y-4 text-2xl font-medium text-gray-900">
          {university} | {department}
        </h1>

        <p className="text-lg text-gray-400">{course}</p>
        <p className="text-base text-gray-500">
          {startDate ? moment(startDate).format("MMM YYYY"): ""} -{" "}
          {endDate ? moment(endDate).format("MMM YYYY") : "Present"}
        </p>
      </div>
    </div>
  );
};

export default EducationCard;

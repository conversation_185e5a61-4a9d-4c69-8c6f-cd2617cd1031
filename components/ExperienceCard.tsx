import React from "react";
import moment from "moment";

export interface ExperienceCardProps {
  company: string;
  position: string;
  startDate: string;
  endDate: string;
  skills: string[];
  responsibilities: string[];
}

const ExperienceCard: React.FC<{
  data: ExperienceCardProps;
}> = ({ data }) => {
  const { company, position, startDate, endDate, skills, responsibilities } =
    data;
  return (
    <div className="relative">
      <div className="absolute left-0 h-4 w-4 -translate-x-1/2 rounded-full bg-dark-tosca"></div>

      <div className="ml-10 rounded-lg border p-10">
        <div className="mb-4 flex items-start justify-between">
          <div className="flex gap-4">
            <div className="flex h-14 items-center justify-center rounded-full bg-gray-200 p-4">
              <svg
                className="h-6 w-6 text-dark-tosca"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                />
              </svg>
            </div>

            <div>
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold">
                  {position} at {company}
                </h2>
                <span className="whitespace-nowrap text-gray-500">
                  {/* {startDate} - {endDate} */}
                  {startDate
                    ? moment(startDate).format("MMM YYYY")
                    : "-"} -{" "}
                  {endDate ? moment(endDate).format("MMM YYYY") : "Present"}
                </span>
              </div>
              <div className="mt-4">
                <h3 className="mb-2 text-sm font-semibold text-gray-500">
                  SKILLS
                </h3>
                <div className="flex flex-wrap gap-2">
                  {skills.map((skill) => (
                    <span
                      key={skill}
                      className="rounded-full bg-dark-tosca px-3 py-1 text-sm text-white"
                    >
                      {skill}
                    </span>
                  ))}
                </div>
              </div>
              <div className="mt-6">
                <h3 className="mb-3 text-sm font-semibold text-gray-500">
                  RESPONSIBILITIES
                </h3>
                <ul
                  className="space-y-3 text-gray-600"
                  style={{ listStyleType: "disc", listStylePosition: "inside" }}
                >
                  {responsibilities.map((responsibility) => (
                    <li key={responsibility} className="text-sm">
                      {responsibility}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExperienceCard;

import { IUser } from "@/interface";
import ExperienceCard from "./ExperienceCard";
import ProjectCard from "./ProjectCard";

const ExperienceSection: React.FC<{
  data: IUser;
}> = ({ data }) => {
  return (
    <div className="my-5 p-6">
      {}
      <div className="space-y-8">
        <h1 className="mb-8 text-2xl font-bold">Experience</h1>

        {data?.experienceDetails?.length ? (
          <div className="relative">
            <div className="absolute bottom-0 left-0 top-0 w-px border-[0.5px] border-dotted border-dark-tosca"></div>

            <div className="space-y-12">
              {data.experienceDetails.map((experience, index) => (
                <ExperienceCard
                  data={{
                    company: experience.companyName,
                    position: experience.designation,
                    startDate: experience.startDate,
                    endDate: experience.endDate,
                    skills: experience.techStack,
                    responsibilities: experience.responsibilities,
                  }}
                />
              ))}
            </div>
          </div>
        ) : null}
      </div>

      {data?.projects?.length ? (
        <section
          id="education"
          className="my-8 space-y-4 rounded-lg border border-gray-100 bg-white px-8 py-6 shadow-lg"
        >
          <h1 className="text-3xl font-medium text-gray-900">Projects</h1>

          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            {data.projects.map((project, index) => (
              <ProjectCard
                key={index}
                project={{
                  description: project.description,
                  title: project.projectName,
                }}
              />
            ))}
          </div>
        </section>
      ) : null}
    </div>
  );
};

export default ExperienceSection;

import App from "../../App";
import { PropsWithChildren } from "react";

type NextPageWithLayout<P = {}> = React.FC<P> & {
  getLayout?: (page: React.ReactNode) => React.ReactNode;
};

const BlankLayout = ({
  children,
  forceLightMode,
}: {
  children: React.ReactNode;
  forceLightMode?: boolean;
}) => {
  return (
    <App>
      <div
        className={
          forceLightMode
            ? "flex min-h-screen items-center justify-center bg-white text-black"
            : ""
        }
      >
        {children}
      </div>
    </App>
  );
};

export default BlankLayout;

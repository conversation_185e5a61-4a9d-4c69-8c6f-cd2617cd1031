import React from "react";

interface Props {
  className?: string;
}
const FullScreenLoader: React.FC<Props> = () => {
  return (
    <div
      className={`absolute flex items-center justify-center h-[100vh] w-[100vw] left-0 top-0 bg-gray-300 z-[1000]`}
    >
      <div
        className="animate-spin-fast inline-block h-10 w-10 animate-spin rounded-full border-4 border-solid   border-[#8D3F42] border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_0.5s_linear_infinite]"
        role="status"
      >
        <span className="!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]">
          Loading...
        </span>
      </div>
    </div>
  );
};

export default FullScreenLoader;

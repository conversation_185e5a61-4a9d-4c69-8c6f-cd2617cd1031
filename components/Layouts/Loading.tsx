import React from "react";

const Loading = () => {
  return (
    <div className="fixed left-0 top-0 z-50 flex h-full w-full items-center justify-center bg-opacity-100">
      <div
        className="animate-spin-fast inline-block h-5 w-5 animate-spin rounded-full border-4 border-solid border-current border-white border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_0.5s_linear_infinite]"
        role="status"
      ></div>
    </div>
  );
};

export default Loading;

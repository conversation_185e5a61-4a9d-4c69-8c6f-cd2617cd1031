import {
  CloseOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import { Images, Strings } from "@/constants";
import { Radio, RadioChangeEvent, Switch, Tooltip } from "antd";
import React, { useState } from "react";
import {
  inviteCandidate,
  inviteCandidateCsv,
} from "@/service/searchTalent.service";

import Image from "next/image";
import Swal from "sweetalert2";

interface Candidate {
  name: string;
  email: string;
  nameError: string | undefined;
  emailError: string | undefined;
  emailSent: boolean | undefined;
}
export const toggleInviteCandidateModal = (
  isOpen: boolean,
  setIsOpen: {
    (value: React.SetStateAction<boolean>): void;
    (arg0: boolean): void;
  }
) => {
  setIsOpen(!isOpen);
};

const InviteCandidate = ({
  isOpen,
  setIsOpen,
}: {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const [skills, setSkills] = useState([{ skill: "", level: "" }]);
  const [inputMethod, setInputMethod] = useState("manual");
  const [csvFile, setCsvFile] = useState<File | null>(null);
  const [candidates, setCandidates] = useState<Candidate[]>([
    {
      name: "",
      email: "",
      nameError: undefined,
      emailError: undefined,
      emailSent: undefined,
    },
  ]);
  const [copied, setCopied] = useState(false);
  const [selectedOption, setSelectedOption] = useState<number>(1);
  const [defineSkills, setDefineSkills] = useState<boolean>(false);
  const [isProctoringEnabled, setIsProctoringEnabled] = useState(true);
  const [isCodingExcerciseEnabled, setIsCodingExcerciseEnabled] = useState(false);

  const onChangeProctoring = (checked: boolean) => {
    setIsProctoringEnabled(checked);
  };
  
  const tooltipContent = (
    <span className="text-xs">
      This adds 15 mins to the test and will test the candidate on a live coding
      exercise.
    </span>
  );
  type Skill = {
    skill: string;
    level: string;
  };
  const tooltipContent1 = (
    <span className="text-xs">
      Use video-based data and tab movements to generate a trust score and
      prevent cheating. We recommend keeping this on; however, you can turn off
      if there are any privacy concerns.
    </span>
  );
  const tooltipContent3 = (
    <span className="text-xs">
      We use a large language modal to generate question so feel free to put any
      skill in any format
    </span>
  );
  const openModal = () => {
    setIsOpen(!isOpen);
  };
  const [loading, setLoading] = useState(false);

  const handleRadioChange = (e: RadioChangeEvent) => {
    const value = e.target.value as number;
    setSelectedOption(value);
    setDefineSkills(value === 2);
  };

  const handleInputChange = (
    type: "name" | "email",
    index: number,
    value: string
  ) => {
    const updatedCandidates = [...candidates];
    updatedCandidates[index][type] = value;

    switch (type) {
      case "name":
        updatedCandidates[index].nameError =
          value.trim().length < 3
            ? "Name must be at least 3 characters."
            : undefined;
        break;
      case "email":
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        updatedCandidates[index].emailError = emailRegex.test(value)
          ? undefined
          : "Please enter a valid email address.";
        break;
      default:
        break;
    }

    setCandidates(updatedCandidates);
  };
  const addCandidate = () => {
    setCandidates([
      ...candidates,
      {
        name: "",
        email: "",
        nameError: undefined,
        emailError: undefined,
        emailSent: undefined,
      },
    ]);
  };

  const deleteCandidate = (index: number) => {
    const updatedCandidates = [...candidates];
    updatedCandidates.splice(index, 1);
    setCandidates(updatedCandidates);
  };
  // const addSkill = () => {
  //   if (skills.length < 5) {
  //     setSkills([...skills, { skill: "", level: "" }]);
  //   }
  // };

  const addSkill = () => {
    const lastSkill = skills[skills.length - 1];

    // If any field in the last skill is incomplete, show alert
    if (!lastSkill.skill || !lastSkill.level) {
      Swal.fire({
        toast: true,
        position: "top-end",
        showConfirmButton: false,
        timer: 3000,
        icon: "error",
        title: "Please fill both skill and level before adding another.",
        padding: "10px 20px",
        customClass: {
          popup: "swal-custom-toast",
        },
      });
      return;
    }

    if (skills.length < 5) {
      setSkills([...skills, { skill: "", level: "" }]);
    }
  };
  
  const deleteSkill = (index: number) => {
    const updatedSkills = [...skills];
    updatedSkills.splice(index, 1);
    setSkills(updatedSkills);
  };

  const handleSkillChange = (index: number, value: string) => {
    const updatedSkills = [...skills];
    updatedSkills[index].skill = value;
    setSkills(updatedSkills);
  };

  const handleLevelChange = (index: number, value: string) => {
    const updatedSkills = [...skills];
    updatedSkills[index].level = value;
    setSkills(updatedSkills);
  };
  
  const onChangeCodingEcxercise = (checked: boolean) => {
    console.log(`switch to ${checked}`);
    setIsCodingExcerciseEnabled(checked);
  };
  const handleCopyClick = () => {
    const newTestLink = `${window.location.origin}/technical-vetting`;
    if (navigator.clipboard) {
      navigator.clipboard
        .writeText(newTestLink)
        .then(() => {
          setCopied(true);
        })
        .catch((error) => {
          console.error("Failed to copy text: ", error);
          setCopied(false);
        });
    } else {
      console.error("Clipboard API not supported in this environment.");
    }
  };

  const sendCandidateInvitation = async (email: string, name: string, skills: Skill[], isProctoringEnabled: boolean, isCodingExcerciseEnabled: boolean) => {
    const response = await inviteCandidate(email, name, skills, isProctoringEnabled, isCodingExcerciseEnabled);

    if (response.error) {
      throw new Error("Failed to send email");
    } else {
      Swal.fire({
        toast: true,
        position: "top-end",
        showConfirmButton: false,
        timer: 3000,
        icon: "success",
        title: response.data?.message,
        padding: "10px 20px",
        customClass: {
          popup: "swal-custom-toast",
        },
      });
    }
  };

  const handleSendInvitation = async (index: number) => {
    const updatedCandidates = [...candidates];
    const candidate = updatedCandidates[index];

    // Validate email before sending
    if (!candidate.email || candidate.emailError) {
      updatedCandidates[index].emailError = "Invalid email address.";
      setCandidates(updatedCandidates);
      return;
    }

    try {
      // Call your email-sending function here
      await sendCandidateInvitation(candidate.email, candidate.name, skills, isProctoringEnabled, isCodingExcerciseEnabled); // Replace with your actual email function
      updatedCandidates[index].emailSent = true;
      updatedCandidates[index].emailError = undefined; // Clear any previous errors
    } catch (error) {
      console.error("Error sending email:", error);
      updatedCandidates[index].emailError =
        "Failed to send email. Please try again.";
      updatedCandidates[index].emailSent = false;
    }

    setCandidates(updatedCandidates);
  };

  const handleInputMethodChange = (method: string) => {
    setInputMethod(method);
    if (method === "manual") {
      setCsvFile(null);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setCsvFile(e.target.files[0]);
    }
  };

  const handleInvitation = async (index: number) => {
    try {
    setLoading(true);

    if (inputMethod === "csv" && csvFile) {
      const response = await inviteCandidateCsv(csvFile);
      if (response.error) {
        throw new Error("Failed to send email");
      } else {
        Swal.fire({
          toast: true,
          position: "top-end",
          showConfirmButton: false,
          timer: 3000,
          icon: "success",
          title: response.data?.message,
          padding: "10px 20px",
          customClass: {
            popup: "swal-custom-toast",
          },
        });
        setCsvFile(null);
      }
    } else {
      await handleSendInvitation(index);
    }
  } catch (err) {
    const errorMessage =
      err instanceof Error ? err.message : "Something went wrong";
  
    console.error(err);
    Swal.fire({
      icon: "error",
      title: "Error",
      text: errorMessage,
    });
  } finally {
    setLoading(false);
  }
  };
  
  return (
    <div>
      {isOpen && (
        <div className="fixed left-0 top-0 z-50 flex h-full w-full items-center justify-center bg-gray-500 bg-opacity-[80%] backdrop-blur-sm">
          <div className="relative">
            <div className="min-h-auto no-scrollbar max-h-[600px] overflow-y-scroll rounded-2xl bg-white p-4 xs:w-[320px] md:w-[500px]">
              <div className="absolute right-2 top-2">
                <button
                  className="flex h-8 w-8 items-center justify-center rounded-full border-transparent bg-white text-black opacity-[80%]"
                  onClick={openModal}
                  title="Close"
                >
                  <CloseOutlined rev={undefined} />
                </button>
              </div>
              <h1 className="text-2xl font-bold text-slate-900">
                {Strings.Inviteacandidate}
              </h1>
              <p className="my-4 font-bold text-slate-500">
                {Strings.Do_you_want}
              </p>
              <div className=" rounded-lg border-2 px-3 py-3  ">
                <Radio.Group
                  className="flex justify-between xs:block xs:space-y-2 md:flex md:space-y-0"
                  name="radiogroup"
                  value={selectedOption}
                  onChange={handleRadioChange}
                >
                  <Radio className="font-semibold" value={1}>
                    {Strings.No_they_can_choose}
                  </Radio>
                  <Radio className="font-semibold" value={2}>
                    {Strings.yes_I_will_define_the_skills}
                  </Radio>
                </Radio.Group>
              </div>
              {defineSkills && (
                <div>
                  <div className="mt-2 flex items-center justify-between">
                    <p className="flex items-center">
                      {Strings.Please_define_the_skills}
                      <Tooltip title={tooltipContent3} placement="right">
                        <ExclamationCircleOutlined
                          rev={undefined}
                          className="ml-2"
                        />
                      </Tooltip>
                    </p>
                    {/* <button className="rounded-full bg-[#8d3f42] bg-opacity-[20%] p-2 text-xs font-bold text-black">
                      {Strings.Save_as_a_new_test}
                    </button> */}
                  </div>
                  {/* <div className="my-2 rounded-lg border-2 px-3 py-3 ">
                    <Radio.Group
                      className="flex justify-between xs:block xs:space-y-2 md:flex md:space-y-0"
                      name="radiogroup"
                      defaultValue={3}
                    >
                      <Radio className="font-semibold" value={3}>
                        {Strings.Choose_from_existing_tests}
                      </Radio>
                      <Radio className="font-semibold" value={4}>
                        {Strings.Define_new_skill_set}
                      </Radio>
                    </Radio.Group>
                  </div> */}
                  {skills.map((skill, index) => (
                    <div
                      key={index}
                      className="mt-2 flex items-center justify-between gap-x-2"
                    >
                      <input
                        value={skill.skill}
                        onChange={(e) => {
                          handleSkillChange(index, e.target.value);
                          console.log(`Skill ${index + 1}: ${e.target.value}`);
                        }}
                        placeholder={`Enter skill #${index + 1}`}
                        className="w-full rounded-lg border-2 p-3 outline-none"
                      />
                      <select
                        value={skill.level}
                        onChange={(e) => {
                          handleLevelChange(index, e.target.value);
                          console.log(`Level ${index + 1}: ${e.target.value}`);
                        }}
                        className={`w-full rounded-lg border-2 px-1 py-3 outline-none xs:text-xs md:text-sm ${
                          index === 0 ? "mr-[22px]" : ""
                        }`}
                        title={`Select level for skill #${index + 1}`}
                      >
                        <option disabled={!index} value="">
                          {Strings.Level_of_difficulty}
                        </option>
                        <option value="Junior">{Strings.Junior}</option>
                        <option value="Mid Level">{Strings.Mid_Level}</option>
                        <option value="Senior">{Strings.Senior}</option>
                      </select>
                      {index > 0 && (
                        <button
                          onClick={() => deleteSkill(index)}
                          className="text-red-500"
                          title="Delete skill"
                        >
                          <DeleteOutlined />
                        </button>
                      )}
                    </div>
                  ))}
                  {skills.length < 5 && (
                    <p
                      className="mt-2 transform cursor-pointer text-sm font-bold text-[#8d3f42] transition-transform hover:scale-[1.01]"
                      onClick={addSkill}
                    >
                      + Add another skill (up to {5 - skills.length} more)
                    </p>
                  )}
                </div>
              )}
              <div className="my-4">
                <p className="mb-2 text-sm font-bold text-black">
                  {Strings.Enter_the_name_email}
                </p>
                <div className="mb-4 flex justify-center space-x-4">
                  <button
                    className={`rounded-lg px-4 py-2 font-medium transition-all ${
                      inputMethod === "manual"
                        ? "bg-[#8d3f42] bg-opacity-20 text-black"
                        : "bg-gray-200 text-gray-600"
                    }`}
                    onClick={() => handleInputMethodChange("manual")}
                  >
                    Manual Entry
                  </button>
                  <span className="flex items-center text-gray-500">Or</span>
                  <button
                    className={`rounded-lg px-4 py-2 font-medium transition-all ${
                      inputMethod === "csv"
                        ? "bg-[#8d3f42] bg-opacity-20 text-black"
                        : "bg-gray-200 text-gray-600"
                    }`}
                    onClick={() => handleInputMethodChange("csv")}
                  >
                    Upload CSV
                  </button>
                </div>
              </div>

              {inputMethod === "manual" && (
                <>
                  {candidates.map((candidate, index) => (
                    <div className="flex items-center justify-between gap-x-2">
                      <div className="h-[60px] w-full">
                        <input
                          type="text"
                          required
                          placeholder="Name"
                          className="w-full rounded-lg border-2 p-3 outline-none"
                          value={candidate.name}
                          onChange={(e) =>
                            handleInputChange("name", index, e.target.value)
                          }
                        />
                        {candidate.nameError && (
                          <p className="-mt-1 text-[11px] text-red-500">
                            {candidate.nameError}
                          </p>
                        )}
                      </div>
                      <div
                        className={`h-[60px] w-full ${
                          index === 0 ? "mr-[22px]" : ""
                        }`}
                      >
                        <input
                          type="email"
                          placeholder="Email address"
                          className="w-full rounded-lg border-2 p-3 outline-none"
                          value={candidate.email}
                          onChange={(e) =>
                            handleInputChange("email", index, e.target.value)
                          }
                        />
                        {candidate.emailError && (
                          <p className="-mt-1 text-[11px] text-red-500">
                            {candidate.emailError}
                          </p>
                        )}
                      </div>
                      {index > 0 && (
                        <button
                          onClick={() => deleteCandidate(index)}
                          className="mb-2 text-red-500"
                          title="Delete candidate"
                        >
                          <DeleteOutlined />
                        </button>
                      )}
                    </div>
                  ))}
                  {/* <p
                    className="transform cursor-pointer text-sm font-bold text-[#8d3f42] transition-transform hover:scale-[1.01]"
                    onClick={addCandidate}
                  >
                    {Strings.Add_another_candidate}
                  </p> */}
                </>
              )}

              {inputMethod === "csv" && (
                <>
                  {loading && (
                    <div className="flex items-center justify-center my-4">
                      <svg
                        className="animate-spin h-6 w-6 text-gray-600"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        />
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8v8H4z"
                        />
                      </svg>
                      <span className="ml-2 text-sm text-gray-600">Sending invitations...</span>
                    </div>
                  )}

                  <div className="mb-4">
                    <div className="flex flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 p-6">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="mb-2 h-10 w-10 text-gray-400"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                        />
                      </svg>
                      <p className="mb-2 text-sm text-gray-500">
                        CSV should have columns for name and email
                      </p>
                      <label className="cursor-pointer rounded-lg bg-[#8d3f42] bg-opacity-20 px-4 py-2 font-medium text-black transition-all hover:bg-opacity-30">
                        {csvFile ? csvFile.name : "Choose CSV file"}
                        <input
                          type="file"
                          accept=".csv"
                          className="hidden"
                          onChange={handleFileChange}
                        />
                      </label>
                      {csvFile && (
                        <div className="mt-4 flex items-center">
                          <span className="mr-2 text-sm text-gray-600">
                            {csvFile.name}
                          </span>
                          <button
                            onClick={() => setCsvFile(null)}
                            className="text-red-500"
                            title="Remove file"
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              className="h-5 w-5"
                              viewBox="0 0 20 20"
                              fill="currentColor"
                            >
                              <path
                                fillRule="evenodd"
                                d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                                clipRule="evenodd"
                              />
                            </svg>
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                </>
              )}
              <div className="my-4 flex max-h-max min-h-14  items-center justify-between rounded-lg border-2  py-3 xs:block xs:px-2 md:flex md:px-14">
                <p className="flex items-center space-x-2">
                  <Switch
                    // defaultChecked
                    onChange={onChangeCodingEcxercise}
                    className="mr-2 bg-gray-300 font-bold text-black-dark-light xs:my-2 md:my-0"
                  />
                  {Strings.Add_coding_exercise}
                  <Tooltip title={tooltipContent} placement="top">
                    <ExclamationCircleOutlined rev={undefined} />
                  </Tooltip>
                </p>
                <p className="flex items-center space-x-2">
                  <Switch
                    defaultChecked
                    onChange={onChangeProctoring}
                    className=" mr-2 bg-gray-300 font-bold text-black-dark-light xs:my-2 md:my-0"
                  />
                  {Strings.Proctoring}
                  <Tooltip title={tooltipContent1} placement="top">
                    <ExclamationCircleOutlined rev={undefined} />
                  </Tooltip>
                </p>
              </div>
              <div className=" flex justify-between xs:block xs:space-y-2 md:flex md:space-y-0">
                <button
                  onClick={handleCopyClick}
                  className="flex transform items-center rounded-full bg-[#8d3f42] bg-opacity-[20%] p-3 text-sm font-bold text-black transition-transform hover:scale-105"
                >
                  <Image
                    src={Images.Copy}
                    alt="/"
                    height={16}
                    width={18}
                    className="mr-1"
                  />
                  {copied ? "Link Copied!" : "Copy the test link instead"}
                </button>
                <button
                  className="transform rounded-full bg-slate-500 bg-opacity-[20%] p-3 text-sm font-bold text-black transition-transform hover:scale-105"
                  onClick={() => handleInvitation(0)} // Assuming you want to send the invitation for the first candidate
                  type="submit"
                >
                  {Strings.Send_invitation}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default InviteCandidate;

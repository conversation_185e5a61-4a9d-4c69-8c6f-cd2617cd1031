import {
  ArrowLeftOutlined,
  CloseOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import React, { useEffect, useState } from "react";
import { Switch, Tooltip } from "antd";

import { Strings } from "@/constants";

const onChange = (checked: boolean) => {
  console.log(`switch to ${checked}`);
};
export const toggleModal1 = (
  managesavedtests: boolean,
  setManagesavedtests: {
    (value: React.SetStateAction<boolean>): void;
    (arg0: boolean): void;
  }
) => {
  setManagesavedtests(!managesavedtests);
};
const Managetests = ({
  managesavedtests,
  setManagesavedtests,
}: {
  managesavedtests: boolean;
  setManagesavedtests: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const [managetests, setManagetests] = useState(false);
  const [skills, setSkills] = useState([{ skill: "", level: "" }]);
  const tooltipContent = <span className="text-xs">{Strings.tooltip}</span>;
  const tooltipContent1 = <span className="text-xs">{Strings.tooltip1}</span>;
  const addSkill = () => {
    if (skills.length < 5) {
      setSkills([...skills, { skill: "", level: "" }]);
    }
  };
  const deleteSkill = (index: number) => {
    const updatedSkills = [...skills];
    updatedSkills.splice(index, 1);
    setSkills(updatedSkills);
  };

  const handleSkillChange = (index: number, value: string) => {
    const updatedSkills = [...skills];
    updatedSkills[index].skill = value;
    setSkills(updatedSkills);
  };

  const handleLevelChange = (index: number, value: string) => {
    const updatedSkills = [...skills];
    updatedSkills[index].level = value;
    setSkills(updatedSkills);
  };

  const openModal5 = () => {
    setManagesavedtests(!managesavedtests);
  };
  const openModal6 = () => {
    setManagetests(!managetests);
  };
  const closeModal = () => {
    setManagetests(false);
    setManagesavedtests(false);
  };
  const repeatCount = 4;
  return (
    <div>
      {managesavedtests && (
        <div className="fixed left-0 top-0 z-50 flex h-full w-full items-center justify-center  bg-gray-500 bg-opacity-[20%] backdrop-blur-sm ">
          <div className="items-center- justify-center- flex- relative rounded-lg bg-white p-5 xs:mx-5 xs:w-full md:mx-0 md:w-[500px]">
            <div className="absolute top-2 right-2">
              <button
                title="Close"
                className="flex h-8 w-8 items-center justify-center rounded-full border-transparent bg-white text-black opacity-[80%]"
                onClick={openModal5}
              >
                <CloseOutlined rev={undefined} />
              </button>
            </div>
            <h1 className="text-xl font-bold text-black">
              {Strings.Manage_tests}
            </h1>
            <div className="my-2 flex justify-center text-center">
              <p className="h-[100px] w-[110px] space-y-3 rounded-lg bg-[#8d3f42] bg-opacity-[20%] px-2 py-5">
                {[...Array(repeatCount)].map((_, index) => (
                  <p
                    key={index}
                    className="rounded-full border-[3px] border-[#8d3f42] border-opacity-[40%]"
                  ></p>
                ))}
              </p>
            </div>
            <div className="text-center">
              <p className="mb-2 text-lg font-semibold text-black">
                {Strings.Nothing_found}
              </p>
              <button
                onClick={openModal6}
                className="xs:w-[270px]- md:w-[450px]- w-full transform rounded-full bg-[#8d3f42] bg-opacity-[20%] p-3 text-base font-semibold text-[#8d3f42] transition-transform hover:scale-105"
              >
                {Strings.Add}
              </button>
            </div>
          </div>
        </div>
      )}
      {managetests && (
        <div className="fixed left-0 top-0 z-50 flex h-full w-full items-center justify-center  bg-gray-500 bg-opacity-[20%] backdrop-blur-sm">
          <div className="relative">
            <div className=" min-h-auto no-scrollbar max-h-[600px] overflow-y-scroll rounded-2xl bg-white p-4 xs:w-[320px] md:w-[500px]">
              <div className="absolute top-2 right-2">
                <button
                  title="Close"
                  className="flex h-8 w-8 items-center justify-center rounded-md bg-white text-black"
                  onClick={closeModal}
                >
                  <CloseOutlined rev={undefined} />
                </button>
              </div>

              <div className="absolute left-4 top-3">
                <button
                  title="Go back"
                  onClick={openModal6}
                  className="transform text-lg font-bold transition-transform hover:scale-105"
                >
                  <ArrowLeftOutlined rev={undefined} />
                </button>
              </div>
              <h1 className="mt-6 text-xl font-bold text-black">
                {Strings.Add_new_test}
              </h1>

              <h1 className="my-2 text-base font-normal text-black">
                {Strings.Name_the_test}
              </h1>
              <input
                placeholder={"Test name"}
                className="w-full rounded-lg border-2 p-3 text-black outline-none"
              />
              <div>
                <div className="justify-between- mt-2 flex items-center">
                  <p className="flex items-center">
                    {Strings.Please_define_the_skills}
                  </p>
                </div>

                {skills.map((skill, index) => (
                  <div
                    key={index}
                    className="mt-2 flex items-center justify-between gap-x-2"
                  >
                    <input
                      value={skill.skill}
                      onChange={(e) => {
                        handleSkillChange(index, e.target.value);
                        console.log(`Skill ${index + 1}: ${e.target.value}`);
                      }}
                      placeholder={`Enter skill #${index + 1}`}
                      className="w-full rounded-lg border-2 p-3 outline-none xs:text-xs md:text-sm"
                    />

                    <label id={`skill-level-label-${index}`} className="sr-only">
                      {Strings.Level_of_difficulty}
                    </label>
                    <select
                      aria-labelledby={`skill-level-label-${index}`}
                      value={skill.level}
                      onChange={(e) => {
                        handleLevelChange(index, e.target.value);
                        console.log(`Level ${index + 1}: ${e.target.value}`);
                      }}
                      className={` w-full rounded-lg border-2 px-1 py-3 outline-none xs:text-xs md:text-sm ${index === 0 ? "mr-[22px]" : ""
                        }`}
                    >
                      <option disabled={!index} value="">
                        {Strings.Level_of_difficulty}
                      </option>
                      <option value="Junior">{Strings.Junior}</option>
                      <option value="Mid Level">{Strings.Mid_Level}</option>
                      <option value="Senior">{Strings.Senior}</option>
                    </select>
                    {index > 0 && (
                      <button
                        title="Delete skill"
                        onClick={() => deleteSkill(index)}
                        className="text-red-500"
                      >
                        <DeleteOutlined />
                      </button>
                    )}
                  </div>
                ))}
                {skills.length < 5 && (
                  <p
                    className="mt-2 transform cursor-pointer text-sm font-bold text-[#8d3f42] transition-transform hover:scale-[1.01]"
                    onClick={addSkill}
                  >
                    + Add another skill (up to {5 - skills.length} more)
                  </p>
                )}
              </div>
              <div className="my-4 flex max-h-max min-h-14 items-center justify-between rounded-lg border-2 xs:block xs:px-2 md:flex  md:px-14">
                <p className="flex items-center space-x-2 ">
                  <Switch
                    onChange={onChange}
                    className="mr-2 bg-gray-300 font-bold text-black xs:my-2 md:my-0"
                  />
                  {Strings.Add_coding_exercise}
                  <Tooltip title={tooltipContent} placement="top">
                    <ExclamationCircleOutlined rev={undefined} />
                  </Tooltip>
                </p>
                <p className="flex items-center space-x-2">
                  <Switch
                    defaultChecked
                    onChange={onChange}
                    className=" mr-2 bg-gray-300 font-bold text-black xs:my-2 md:my-0"
                  />
                  {Strings.Proctoring}
                  <Tooltip title={tooltipContent1} placement="top">
                    <ExclamationCircleOutlined rev={undefined} />
                  </Tooltip>
                </p>
              </div>
              <div className="flex justify-center">
                <button className="w-full transform rounded-full bg-[#8d3f42] bg-opacity-[20%] p-3 text-base font-semibold text-[#8d3f42] transition-transform hover:scale-105">
                  {Strings.Save}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Managetests;

import React, { useEffect } from "react";

import { CloseOutlined } from "@ant-design/icons";

export const toggleModal2 = (
  quickdemo: boolean,
  setQuickdemo: {
    (value: React.SetStateAction<boolean>): void;
    (arg0: boolean): void;
  }
) => {
  setQuickdemo(!quickdemo);
};

const Quickdamo = ({
  quickdemo,
  setQuickdemo,
}: {
  quickdemo: boolean;
  setQuickdemo: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const openModal = () => {
    setQuickdemo(!quickdemo);
  };
  return (
    <div>
      {quickdemo && (
        <div className="fixed left-0 top-0 z-50 flex h-full w-full items-center justify-center  bg-gray-500 bg-opacity-[20%] backdrop-blur-sm ">
          <div className="relative flex items-center justify-center rounded-xl bg-white p-5 xs:mx-5 xs:w-full md:mx-0 md:w-[460px] ">
            <div className="absolute right-2 top-2">
              <button
                className="flex h-8 w-8 items-center justify-center rounded-full border-transparent bg-white text-black opacity-[80%]"
                onClick={openModal}
                title="Close"
              >
                <CloseOutlined rev={undefined} />
              </button>
            </div>
            <div className="space-y-2">
              <h1 className="text-xl font-bold text-black"> Quick demo</h1>
              <video className="rounded-xl" controls>
                <source
                  src="https://storage.googleapis.com/eremotehire/QuickDemo/Invite_Candidate_Client_Portal.mp4"
                  height={60}
                  width={60}
                  type="video/mp4"
                />
                Your browser does not support the video tag.
              </video>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Quickdamo;

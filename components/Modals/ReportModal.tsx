import { CloseOutlined, DownloadOutlined } from "@ant-design/icons";
import React, { useEffect, useRef, useState } from "react";

import { Strings } from "@/constants";
import html2canvas from "html2canvas";
import jsPDF from "jspdf";

export const toggleReportModal = (
  reportModal: boolean,
  setReportModal: React.Dispatch<React.SetStateAction<boolean>>
) => {
  setReportModal(!reportModal);
};

const ReportModal = ({
  reportModal,
  setReportModal,
}: {
  reportModal: boolean;
  setReportModal: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const reportRef = useRef<HTMLDivElement>(null);
  const [animating, setAnimating] = useState(false);
  const [date, setDate] = useState(new Date());

  const formattedDate = date.toLocaleDateString("en-GB", {
    day: "2-digit",
    month: "short",
    year: "numeric",
  });

  const openModal = () => {
    setAnimating(true);
    setTimeout(() => {
      setReportModal(false);
      setAnimating(false);
    }, 500);
  };

  const handleClickOutside1 = (event: MouseEvent) => {
    if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
      openModal();
    }
  };

  useEffect(() => {
    if (reportModal) {
      document.addEventListener("mousedown", handleClickOutside1);
    } else {
      document.removeEventListener("mousedown", handleClickOutside1);
    }
    return () => document.removeEventListener("mousedown", handleClickOutside1);
  }, [reportModal]);

  const handleDownloadReport = async () => {
    if (!reportRef.current) return;

    const element = reportRef.current;

    try {
      const canvas = await html2canvas(element);
      const imgData = canvas.toDataURL("image/png");
      const pdf = new jsPDF("p", "mm", "a4");

      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();

      const imgWidth = pageWidth;
      const imgHeight = (canvas.height * imgWidth) / canvas.width;

      let position = 0;

      pdf.addImage(imgData, "PNG", 0, position, imgWidth, imgHeight);

      if (imgHeight > pageHeight) {
        position = imgHeight - pageHeight;
        while (position >= 0) {
          pdf.addPage();
          pdf.addImage(imgData, "PNG", 0, -position, imgWidth, imgHeight);
          position -= pageHeight;
        }
      }

      pdf.save(`Report_${formattedDate}.pdf`);
    } catch (error) {
      console.error("Error while generating PDF", error);
    }
  };

  return (
    <div>
      {(reportModal || animating) && (
        <div className="fixed left-0 top-0 z-50 flex h-full w-full items-end justify-center bg-gray-500 bg-opacity-[80%] backdrop-blur-sm">
          <div className="fixed right-7 top-6">
            <button
              className="flex h-8 w-8 items-center justify-center rounded-full border-transparent bg-white text-black opacity-[80%]"
              onClick={openModal}
              title="Close"
            >
              <CloseOutlined rev={undefined} />
            </button>
          </div>
          <div
            ref={modalRef}
            className={`${
              reportModal && !animating ? "slide-in" : "slide-out"
            } items-center- xs:justify-center- md:justify-start- no-scrollbar flex h-[90%] w-[100%] overflow-y-scroll bg-white xs:px-3 md:px-6 lg:px-12`}
          >
            <div>
              <div className="my-10 flex justify-between xs:w-[310px]  md:w-[950px]">
                <button
                  onClick={handleDownloadReport}
                  className="flex items-center space-x-2 text-base font-bold text-[#8d3f42]"
                >
                  <DownloadOutlined rev={undefined} />
                  <p>{Strings.Download_report}</p>
                </button>
                <div className="flex items-center space-x-2 ">
                  <button className="rounded-full bg-slate-500 bg-opacity-[20%] p-2 text-black hover:shadow-sm">
                    {Strings.Contected}
                  </button>
                  <h1 className="cursor-pointer text-sm font-bold text-red-500">
                    {Strings.Archive}
                  </h1>
                </div>
              </div>
              <div
                ref={reportRef}
                className="rounded-md border-2 p-4 shadow-lg xs:w-[310px] md:w-[950px]"
              >
                <div className="mt-5 flex justify-between">
                  <div className="text-[#000]">
                    <h1 className="text-base font-semibold">
                      {Strings.REPORT_ID}
                    </h1>
                    <h1 className="text-base font-semibold">Name: Jhon Doe</h1>
                    <h1 className="text-base font-semibold">
                      {Strings.PARTH_MAIL}
                    </h1>
                  </div>
                  <h1 className="text-base font-semibold text-[#000]">
                    Date: {formattedDate}
                  </h1>
                </div>
                <p className="my-5 border"></p>
                <h1 className="text-lg font-bold text-slate-500">
                  {Strings.TECHNICAL_SKILLS}
                </h1>
                <p className="space-y-7">
                  <p className="text-lg font-bold text-black">
                    {Strings.Node_js}
                  </p>
                  <p className="text-base font-bold">
                    {Strings.SELF_RATING_INTERMIDIATE}
                  </p>
                  <p className="text-base font-bold text-lime-500">
                    {Strings.AI_ASSESMENT}
                  </p>
                  <p>{Strings.GPT_PARA}</p>
                  <p>{Strings.RATING_BY_AI}</p>
                  <p className="text-lg font-bold text-black">
                    {Strings.React_js}
                  </p>
                  <p className="text-base font-bold">
                    {Strings.SELF_RATING_BEGIN}
                  </p>
                  <p className="text-base font-bold text-lime-500">
                    {Strings.AI_ASSESMENT}
                  </p>
                  <p>{Strings.RATING_AI_PARA}</p>
                </p>
                <div className="mt-5 flex justify-end">
                  <p className="text-base font-normal text-black">
                    {Strings.POWERED_BY}
                    <span className="ml-1 text-[#8d3f42]">
                      {Strings.EREMOTEHIRE}
                    </span>
                  </p>
                </div>
              </div>
              <div className="my-5 h-[100px] w-[950px] bg-white"></div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ReportModal;

import { Images, Strings } from "@/constants";
import React, { useEffect, useRef, useState } from "react";
import Upgradeplane, { UpgradeplaneProps } from "./Upgradeplane";

import { CloseOutlined } from "@ant-design/icons";
import { Switch } from "antd";
import { ISubscription, IUser } from "@/interface";

export const toggleUpgradeModal = (
  upgradeplan: boolean,
  setUpgradeplan: {
    (value: React.SetStateAction<boolean>): void;
    (arg0: boolean): void;
  }
) => {
  setUpgradeplan(!upgradeplan);
};

const UpgradePlaneModal = ({
  upgradeplan,
  setUpgradeplan,
}: {
  upgradeplan: boolean;
  setUpgradeplan: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const [user, _] = useState(() => {
    if (typeof window === "undefined") return null;
    const userJson = localStorage.getItem("userData");
    return JSON.parse(userJson!) as IUser;
  });

  const modalRef = useRef<HTMLDivElement>(null);
  const [yearly, setYearly] = React.useState(false);
  const [animating, setAnimating] = useState(false);

  const openModal3 = () => {
    setAnimating(true);
    setTimeout(() => {
      setUpgradeplan(false);
      setAnimating(false);
    }, 500);
  };

  const handleClickOutside1 = (event: MouseEvent) => {
    if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
      openModal3();
    }
  };

  useEffect(() => {
    if (upgradeplan) {
      document.addEventListener("mousedown", handleClickOutside1);
    } else {
      document.removeEventListener("mousedown", handleClickOutside1);
    }
    return () => document.removeEventListener("mousedown", handleClickOutside1);
  }, [upgradeplan]);

  const checkYearly = () => {
    setYearly((prevYearly) => !prevYearly);
  };

  const isSubscribed = user?.subscription?.status === "paid";

  const data: UpgradeplaneProps[] = [
    {
      imageSrc: Images.Earlystage,
      title: "EARLY STAGE",
      price: "60",
      yearlyPrice: "650",
      reportsPerMonth: "20 reports/month",
      seats: "Up to 3 seats",
      dedicatedSupport: false,
      customForm: false,
      atsIntegrations: false,
      customFeatures: false,
      buttonText: isSubscribed ? "Upgrade" : "Subscribe",
      titleColor: "text-black",
      backgroundColor: " bg-[#f5f4f8]",
      yearly: true,
      paymentLinkMonthly: "https://buy.stripe.com/test_3cseWO3aFcFmf8AcMM",
      paymentLinkYearly: "https://buy.stripe.com/test_aEUeWO8uZ8p64tW149",
    },
    {
      imageSrc: Images.Scale,
      title: "SCALE",
      price: "275",
      yearlyPrice: "2700",
      reportsPerMonth: "100 reports/month",
      seats: "Up to 10 seats",
      dedicatedSupport: true,
      customForm: false,
      atsIntegrations: false,
      customFeatures: false,
      buttonText: isSubscribed ? "Upgrade" : "Subscribe",
      titleColor: "text-[#009C20]",
      backgroundColor: "bg-[#EBFCEF]",
      yearly: true,
      paymentLinkMonthly: "https://buy.stripe.com/test_00g8yq7qV20Id0s004",
      paymentLinkYearly: "https://buy.stripe.com/test_fZe8yq5iN5cU2lO6or",
    },
    {
      imageSrc: Images.Enterprise,
      title: "ENTERPRISE",
      price: "Let's talk",
      yearlyPrice: "Let's talk",
      reportsPerMonth: "More than 100 reports/month",
      seats: "Unlimited seats",
      dedicatedSupport: true,
      customForm: true,
      atsIntegrations: true,
      customFeatures: true,
      buttonText: "Contact Us",
      titleColor: "text-[#711CA6]",
      backgroundColor: "bg-[#EFDDF8]",
      yearly: false,
      paymentLinkMonthly:
        "https://calendly.com/dharmendra-eremotehire/gpt-vetting-demo?month=2025-02",
      paymentLinkYearly:
        "https://calendly.com/dharmendra-eremotehire/gpt-vetting-demo?month=2025-02",
    },
  ];

  return (
    <div>
      {(upgradeplan || animating) && (
        <div className="fixed left-0 top-0 z-50 flex h-full w-full items-end justify-center bg-gray-500 bg-opacity-[80%] backdrop-blur-sm">
          <div className="absolute right-7 top-6">
            <button
              className={`flex h-8 w-8 items-center justify-center rounded-full border-transparent bg-white text-black opacity-[80%] ${
                upgradeplan || animating ? "opacity-100" : "opacity-0"
              }`}
              onClick={openModal3}
            >
              <CloseOutlined rev={undefined} />
            </button>
          </div>
          <div
            ref={modalRef}
            className={` ${
              upgradeplan && !animating ? "slide-in" : "slide-out"
            } items-center- justify-center- flex- no-scrollbar h-[90%] w-[100%] overflow-y-scroll bg-white xs:px-6 xs:py-6 md:px-6 md:py-0 lg:px-12 `}
          >
            <h1 className="mt-7 text-center text-4xl font-bold text-black">
              {user?.subscription?.status === "paid"
                ? Strings.Upgrade_your_plane
                : Strings.Subscribe_to_a_plan}
            </h1>
            <div className="my-7 flex justify-center">
              <div className="flex w-[250px] justify-center rounded-lg border-2 p-4 text-black">
                <p className={`text-base ${!yearly ? "font-semibold" : ""}`}>
                  {Strings.Monthly}
                </p>
                <Switch
                  onChange={checkYearly}
                  className="mx-2 bg-gray-300 font-bold text-black-dark-light"
                />
                <p className={`text-base ${yearly ? "font-semibold" : ""}`}>
                  {Strings.Yearly}
                </p>
              </div>
            </div>
            <div className="m-auto- max-w-screen-2xl-">
              <div className="flex justify-center xs:flex-col xs:space-y-4 lg:flex-row lg:space-x-4 lg:space-y-0">
                {data.map((item, index) => (
                  <Upgradeplane key={index} {...item} yearly={yearly} />
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UpgradePlaneModal;

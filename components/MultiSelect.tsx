import React, { useState, useEffect, useRef } from "react";
import dropdownIcon from "@/public/assets/svg/dropdownIcon.svg";
import Image from "next/image";
import Chip from "./Chip";

type Skill = {
  label: string;
  value: string;
};

type MultiSelectDropdownProps = {
  label: string;
  data: Skill[];
  selectedValues: string[];
  onChange: (selected: string[]) => void;
  placeholder: string;
};

const MultiSelectDropdown: React.FC<MultiSelectDropdownProps> = ({
  label,
  data,
  selectedValues,
  onChange,
  placeholder,
}) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const toggleDropdown = () => setIsDropdownOpen((prev) => !prev);

  const handleSelectionChange = (value: string) => {
    if (selectedValues.includes(value)) {
      onChange(selectedValues.filter((item) => item !== value));
    } else {
      onChange([...selectedValues, value]);
    }
  };

  const getSelectedLabels = () => {
    return data
      .filter((skill) => selectedValues.includes(skill.value))
      .map((skill) => {
        return (
          <Chip
            handleClick={() => handleSelectionChange(skill.value)}
            key={skill.value}
          >
            {skill.label}
          </Chip>
        );
      });
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (
      dropdownRef.current &&
      !dropdownRef.current.contains(event.target as Node)
    ) {
      setIsDropdownOpen(false);
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div ref={dropdownRef} className="relative mt-3">
      <label
        htmlFor={label}
        className="mb-2 block text-sm font-medium text-gray-900 dark:text-white"
      >
        {label}
      </label>
      <div
        className="flex w-full cursor-pointer items-center justify-between rounded-lg border border-[#8d3f42] bg-transparent px-[10px] py-[14px] text-[16px] text-gray-900 outline-none dark:text-white"
        onClick={toggleDropdown}
      >
        {selectedValues.length > 0 ? (
          <div className="flex gap-2">{getSelectedLabels()}</div>
        ) : (
          placeholder
        )}
        <Image
          src={dropdownIcon}
          className={`h-6 w-6 transform transition-transform ${
            isDropdownOpen ? "rotate-180" : ""
          }`}
          alt="dropdown"
        />
      </div>
      {isDropdownOpen && (
        <div className="absolute left-0 top-full z-10 mt-2 w-full rounded-lg border bg-white p-2 shadow-[inset_0px_0px_18px_-17px_rgba(0,_0,_0,_0.8)]">
          {data.map((skill) => (
            <label
              key={skill.value}
              className="flex items-center px-2 py-1 hover:bg-gray-100"
            >
              <input
                type="checkbox"
                className="mr-2"
                checked={selectedValues.includes(skill.value)}
                onChange={() => handleSelectionChange(skill.value)}
              />
              {skill.label}
            </label>
          ))}
        </div>
      )}
    </div>
  );
};

export default MultiSelectDropdown;

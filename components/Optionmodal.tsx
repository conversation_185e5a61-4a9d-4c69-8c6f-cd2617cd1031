import Image from "next/image";
import { Images } from "@/constants";
import React from "react";

interface OptionmodalProps {
  isImageShow?: boolean;
  buttonText: string;
  label: string;
  imageSrc: any;
  isSelected: boolean;
  onClick: () => void;
  imageClassName?: string;
}

const Optionmodal: React.FC<OptionmodalProps> = ({
  isImageShow = true,
  buttonText,
  label,
  imageSrc,
  isSelected,
  onClick,
  imageClassName = "",
}) => {
  return (
    <div>
      <button
        className={`relative flex h-12 items-center justify-center rounded-lg border border-[#000] bg-[#fff] px-2 dark:border-[#fff] dark:bg-[#000] ${
          isSelected ? "border-opacity-100" : "border-opacity-70"
        }`}
        onClick={() => {
          onClick();
          // console.log(`Selected option: ${label}`);
        }}
      >
        <div className={`flex items-center justify-center`}>
          <div>
            {isImageShow && (
              <Image
                src={imageSrc}
                alt="/"
                height={500}
                width={500}
                className={`mr-2 h-[30px] w-[30px] rounded-full ${imageClassName}`}
              />
            )}
          </div>
          <h1
            className={`font-light text-[#000] dark:text-[#fff] xs:text-sm lg:text-lg`}
          >
            {label}
          </h1>
        </div>
        {isSelected && (
          <Image
            src={Images.Checkicon}
            alt="/"
            height={500}
            width={500}
            className="absolute -right-1 -top-1 h-5 w-5"
          />
        )}
      </button>
    </div>
  );
};

export default Optionmodal;

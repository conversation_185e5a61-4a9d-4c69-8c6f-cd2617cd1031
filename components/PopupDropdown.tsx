import { Images } from "@/constants";
import Image from "next/image";
import React, { useEffect, useRef, useState } from "react";

const PopupDropdown: React.FC<{
  items: { title: string; onClick: () => void }[];
}> = ({ items }) => {
  const popoverRef = useRef<HTMLDivElement | null>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const handleClickOutside = (event: any) => {
      if (popoverRef.current && !popoverRef.current.contains(event.target)) {
        setIsVisible(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);
  return (
    <div className="relative">
      <div
        onClick={(e) => {
          e.stopPropagation();
          setIsVisible(!isVisible)
        }}
        className="flex h-7 w-7 items-center justify-center rounded-lg border-[1.5px] border-[#dbdbdb] dark:bg-white"
      >
        <Image
          src={Images.dots}
          width={500}
          height={500}
          alt={"/"}
          className="h-5 w-5 cursor-pointer"
        />
      </div>

      {isVisible && (
        <div
          ref={popoverRef}
          id="popover"
          className="absolute right-0 top-11 z-10 rounded-lg border bg-white p-2 text-base font-semibold shadow-md"
        >
          {items.map((item, index) => (
            <p
              key={index}
              onClick={()=> {
                item.onClick();
                setIsVisible(false);
              }}
              className={`w-full cursor-pointer whitespace-nowrap px-2 py-1 text-start text-black ${
                index !== items.length - 1 && "border-b"
              }`}
            >
              {item.title}
            </p>
          ))}
        </div>
      )}
    </div>
  );
};

export default PopupDropdown;

import React from "react";
import {
  LinkedinOutlined as Linkedin,
  EnvironmentOutlined as MapPin,
  MailOutlined as Mail,
  PhoneOutlined as Phone,
} from "@ant-design/icons";
import Image from "next/image";
import Link from "next/link";

interface ProfileProps {
  title: string;
  location: string;
  email: string;
  phone: string;
  profilePicture: string;
  linkedInUrl: string;
  interviewDate?: string;
  firstName: string;
  lastName: string;
}

const ProfileInfoCard: React.FC<ProfileProps> = ({
  title,
  location,
  email,
  phone,
  profilePicture,
  linkedInUrl,
  interviewDate,
  firstName,
  lastName,
}) => {
  return (
    <div className="w-full bg-gradient-to-b from-gray-50 to-white p-6 shadow-sm">
      <div className="flex flex-col gap-4 md:flex-row md:items-start md:gap-6">
        <div className="h-24 w-24 overflow-hidden rounded-lg bg-gray-100">
          <Image
            src={
              profilePicture
                ? profilePicture
                : `https://avatar.iran.liara.run/username?username=${firstName}+${lastName}`
            }
            alt={`avatar`}
            width={96}
            height={96}
            className="h-full w-full object-cover"
          />
        </div>

        <div className="flex flex-1 flex-col gap-4">
          <div className="space-y-1.5">
            <h1 className="text-2xl font-semibold text-gray-900">{`${firstName} ${lastName}`}</h1>
            <div className="flex flex-wrap items-center gap-2">
              <span className="text-gray-600">{title}</span>
              {/* <Link
                href={linkedInUrl}
                className="inline-flex items-center gap-1.5 rounded-full bg-gray-100 px-3 py-1 text-sm text-dark-tosca transition-all hover:bg-gray-200"
              >
                <Linkedin className="h-4 w-4" />
                LinkedIn profile
              </Link> */}
            </div>
          </div>

          <div className="flex flex-wrap gap-3 text-sm text-gray-600">
            {location ? (
              <div className="inline-flex items-center gap-1.5">
                <MapPin className="h-4 w-4" />
                {location}
              </div>
            ) : null}
            {/* <div className="inline-flex items-center gap-1.5">
              <Mail className="h-4 w-4" />
              {email}
            </div>
            <div className="inline-flex items-center gap-1.5">
              <Phone className="h-4 w-4" />
              {`+${phone}`}
            </div> */}
          </div>
        </div>

        {interviewDate && (
          <div className="text-right text-sm text-gray-500">
            AI interview completed on {interviewDate}
          </div>
        )}
      </div>
    </div>
  );
};

export default ProfileInfoCard;

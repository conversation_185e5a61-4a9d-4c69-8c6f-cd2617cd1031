import React from "react";

export interface IProjectProps {
  title: string;
  description: string;
}

const ProjectCard: React.FC<{
  project: IProjectProps;
}> = ({ project }) => {
  return (
    <div className="rounded-lg border border-gray-100 bg-white p-6">
      <h2 className="mb-4 text-2xl font-bold tracking-wide">{project.title}</h2>

      <div className="space-y-3">
        <h3 className="text-lg font-medium uppercase tracking-wide text-gray-500">
          About the Project
        </h3>

        <p className="leading-relaxed text-base text-gray-600">{project.description}</p>
      </div>
    </div>
  );
};

export default ProjectCard;

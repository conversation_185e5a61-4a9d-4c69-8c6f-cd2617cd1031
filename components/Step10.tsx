import { CloseOutlined, RightOutlined } from "@ant-design/icons";
import React, { useState } from "react";

import { Strings } from "@/constants";

interface Step10Props {
  setStep: React.Dispatch<React.SetStateAction<number>>;
}

const Step10: React.FC<Step10Props> = ({ setStep }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isOpen1, setIsOpen1] = useState(false);

  const openModal = () => {
    setIsOpen(!isOpen);
  };

  return (
    <>
      <div>
        <h1 className="text-center text-3xl font-bold text-black">
          {Strings.Pre_defined_skill_set}
        </h1>
        <p className="my-5 w-[630px] text-center font-bold text-black">
          {Strings.Here_are_the_skills}
        </p>
        <div className="space-y-4">
          <h1 className="flex w-[580px] justify-between text-black">
            <p className="font-bold ">{Strings.Main_skills}</p>
            <p className="w-[280px] border-transparent font-bold">
              {Strings.Rate_yourself}
            </p>
          </h1>
          <div className="flex w-[580px] justify-between text-black">
            <p className="w-[280px] rounded-md border-2 p-2 outline-none">
              {Strings.Node_js}
            </p>
            <p className="w-[280px] rounded-md border-2 p-2 outline-none">
              {Strings.Senior}
            </p>
          </div>
          <div className="flex w-[580px] justify-between text-black">
            <p className="w-[280px] rounded-md border-2 p-2 outline-none">
              {Strings.AWS}
            </p>
            <p className="w-[280px] rounded-md border-2 p-2 outline-none">
              {Strings.Mid_level}
            </p>
          </div>
          <div className="flex w-[580px] justify-between text-black">
            <p className="w-[280px] rounded-md border-2 p-2 outline-none">
              {Strings.React_js}
            </p>
            <p className="w-[280px] rounded-md border-2 p-2 outline-none">
              {Strings.Junior}
            </p>
          </div>
          <div className="flex w-[580px] justify-between text-black">
            <p className="w-[280px] rounded-md border-2 p-2 outline-none">
              {Strings.Project_management}
            </p>
            <p className="w-[280px] rounded-md border-2 p-2 outline-none">
              {Strings.Junior}
            </p>
          </div>
        </div>
        <div className="my-5 flex justify-center">
          <button
            onClick={openModal}
            className="flex w-36 items-center justify-end rounded-full bg-[#8d3f42] p-2 text-lg text-white  hover:bg-[#161616] "
          >
            {Strings.Start_test}
            <div className="ml-2 flex h-8 w-8 items-center justify-center rounded-full border-transparent bg-slate-500 bg-opacity-[20%]">
              <RightOutlined />
            </div>
          </button>
        </div>
        <p className=" text-center font-medium text-slate-500">
          {Strings.Note}
        </p>
      </div>
      {isOpen && (
        <div className="fixed left-0 top-0 z-50 flex h-full w-full items-center  justify-center bg-gray-500 bg-opacity-[80%] backdrop-blur-sm">
          <div className="relative w-[500px] rounded-2xl bg-white p-5">
            <div className="absolute -right-10 top-2">
              <button
                className="flex h-8 w-8 items-center justify-center rounded-full border-transparent bg-white text-black opacity-[80%]"
                onClick={openModal}
              >
                <CloseOutlined rev={undefined} />
              </button>
            </div>
            <h1 className="text-2xl font-bold text-black">
              {Strings.IMPORTANT_POINTS}
            </h1>
            <div className="text-justify- my-7 space-y-4 px-5">
              <ul className="list-disc text-base font-semibold text-black">
                <li>{Strings.Please_note}</li>
              </ul>
              <ul className="list-disc text-base font-semibold text-black">
                <li>{Strings.We_encourage}</li>
              </ul>
              <ul className="list-disc text-base font-semibold text-black">
                <li>{Strings.You_cant}</li>
              </ul>
            </div>
            <div className=" flex justify-end">
              <button
                onClick={() => {
                  setIsOpen(false);
                  setIsOpen1(true);
                }}
                className="flex w-36 items-center justify-center rounded-full bg-[#8d3f42] p-2 text-lg text-white hover:bg-[#161616] "
              >
                {Strings.Start_test_now}
              </button>
            </div>
          </div>
        </div>
      )}
      {isOpen1 && (
        <div className="fixed left-0 top-0 z-50 flex h-full w-full items-center justify-center bg-gray-500 bg-opacity-[80%] backdrop-blur-sm">
          <div className="w-[520px] space-y-4 rounded-2xl bg-white p-5 text-justify">
            <h1 className="text-3xl font-bold text-black">
              {Strings.Camera_microphone}
            </h1>
            <p className="text-base font-semibold text-black">
              {Strings.You_re_about}
            </p>
            <p className="text-base font-semibold text-black">
              {Strings.Once_you_ve}
            </p>
            <p className="text-xl font-bold text-black">
              {Strings.Best_of_luck}
            </p>
            <div className="flex justify-end">
              <button
                onClick={() => {
                  setStep((prevStep) => prevStep + 1);
                }}
                className="flex w-28 items-center justify-center rounded-full bg-[#8d3f42] p-2 text-lg text-white  hover:bg-[#161616]"
              >
                {Strings.Continue}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default Step10;

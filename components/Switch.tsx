import React from "react";

const Switch: React.FC<{
  handleQuery: (key: string, value: boolean) => void;
  field: string;
}> = ({ handleQuery, field }) => {
  return (
    <label className=" relative ml-[10px] cursor-pointer items-center">
      <input
        type="checkbox"
        className="peer sr-only"
        onChange={(e) => handleQuery(field, e.target.checked)}
      />
      <div className="peer h-6 w-11 rounded-full bg-gray-200 after:absolute after:start-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-[#8D3F42] peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:peer-focus:ring-[#8D3F42]" />
    </label>
  );
};

export default Switch;

import "react-phone-input-2/lib/style.css";

import { CheckOutlined, LeftOutlined, RightOutlined } from "@ant-design/icons";
import { Images, Strings } from "@/constants";
import PhoneInput, { CountryData } from "react-phone-input-2";
import React, { useEffect, useState } from "react";

import FileUploader from "./FileUploader";
import Image from "next/image";
import { Line } from "rc-progress";
import Step10 from "./Step10";
import Voicerecorder from "./Voice-recorder";

const Technicalform = () => {
  const [step, setStep] = useState(1);
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [position, setPosition] = useState("");
  const [experience, setExperience] = useState("");
  const [linkedinURL, setLinkedinURL] = useState("");
  const [file, setFile] = useState<File | null>(null);
  const [error, setError] = useState("");
  const progress = ((step - 1) / 7) * 100;

  const handleNext = () => {
    let errorMessage = "";

    switch (step) {
      case 2:
        if (!name.trim()) {
          errorMessage = "Name is required.";
        }
        break;
      case 3:
        if (!email.trim()) {
          errorMessage = "Email is required.";
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
          errorMessage = "Please enter a valid email address.";
        }
        break;
      case 4:
        if (!phoneNumber.trim() || phoneNumber.trim().length < 10) {
          errorMessage = "Please enter a valid phone number.";
        }
        break;
      case 5:
        if (!position.trim()) {
          errorMessage = "Position is required.";
        }
        break;
      case 6:
        if (!experience.trim() || isNaN(Number(experience))) {
          errorMessage = "Please enter a valid number for experience.";
        }
        break;
      case 7:
        if (!linkedinURL.trim()) {
          errorMessage = "LinkedIn URL is required.";
        } else if (
          !/^https?:\/\/(www\.)?linkedin\.com\/.*$/.test(linkedinURL)
        ) {
          errorMessage = "Please enter a valid LinkedIn URL.";
        }
        break;
      case 8:
        if (!file) {
          errorMessage = "Please upload a file.";
        }
        break;
      default:
        break;
    }

    if (errorMessage) {
      setError(errorMessage);
      return false;
    }

    setError("");
    setStep((prevStep) => prevStep + 1);
    return true;
  };

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Enter" && !event.shiftKey) {
        event.preventDefault();
        if (step < 8) {
          handleNext();
        }
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [step, name, email, phoneNumber, position, experience, linkedinURL, file]);

  const handleSubmit = () => {
    setStep(step + 1);
  };

  return (
    <div>
      {step > 1 && step <= 8 && (
        <div className="fixed right-12 top-12 flex items-center gap-x-2 text-lg font-bold text-black">
          <Line
            percent={progress}
            strokeWidth={4}
            trailWidth={4}
            strokeColor="#22c55e"
            trailColor="#cbd5e1"
            className="w-40 rounded-lg"
          />
          <h1>{step - 1}/7</h1>
        </div>
      )}
      {step === 1 && (
        <>
          <div className=" flex justify-center">
            <Image
              className=" h-[35px] w-[165px]"
              src={Images.REMOTEHIRELOGODark}
              alt="logo"
              height={50}
              width={50}
            />
          </div>
          <div className="mt-12 w-[360px] text-center">
            <h1 className="text-lg font-medium text-black">
              {Strings.THANK_YOU_FOR_YOUR_INTEREST}
            </h1>
            <p className="text-lg font-medium text-black">
              There are <span className="font-bold">2 steps</span> to this
              application:
            </p>
          </div>
          <div className="my-10 flex justify-center">
            <div className="h-28 w-72 space-y-4 rounded-xl border-2 p-4">
              <h1 className="flex items-center space-x-2">
                <p className="flex h-8 w-8 items-center justify-center rounded-full bg-[#8d3f42] bg-opacity-[20%] font-bold text-black">
                  1
                </p>
                <p className="font-semibold text-black">
                  {Strings.Your_basic_details}
                </p>
              </h1>
              <h1 className="flex items-center space-x-2">
                <p className="flex h-8 w-8 items-center justify-center rounded-full bg-[#8d3f42] bg-opacity-[20%] font-bold text-black">
                  2
                </p>
                <p className="font-semibold text-black">
                  {Strings.A_technical_test}
                </p>
              </h1>
            </div>
          </div>
          <div className="flex justify-center">
            <button
              onClick={() => setStep(step + 1)}
              className="flex w-32 items-center justify-end rounded-full bg-[#8d3f42] p-2 text-lg text-white  hover:bg-[#161616] "
            >
              {Strings.Continue}
              <div className="ml-2 flex h-8 w-8 items-center justify-center rounded-full border-transparent bg-slate-500 bg-opacity-[20%]">
                <RightOutlined />
              </div>
            </button>
          </div>
        </>
      )}
      {step === 2 && (
        <div className="space-y-7">
          <h1 className="font-bold text-black xs:text-xl md:text-2xl lg:text-4xl">
            {Strings.Q1}
          </h1>
          <input
            type="text"
            required
            value={name}
            onChange={(e) => {
              setName(e.target.value);
              setError("");
            }}
            placeholder="Your Name"
            className={`rounded-lg border-[2px] p-2 py-5 text-black outline-none xs:w-full xs:text-base md:w-[600px] lg:text-xl xl:w-[900px] xl:text-2xl ${
              error ? "border-red-500" : ""
            }`}
          />
          <div className="space-y-2">
            <p className="border " />
            {error && <p className="fixed text-red-500 ">{error}</p>}
          </div>
        </div>
      )}
      {step === 3 && (
        <>
          <div className="space-y-7">
            <h1 className="font-bold text-black xs:text-xl md:text-2xl lg:text-4xl">
              {Strings.Q2}
            </h1>
            <input
              type="email"
              value={email}
              onChange={(e) => {
                setEmail(e.target.value);
                setError("");
              }}
              placeholder="Your Email"
              className={`rounded-lg border-[2px] p-2 py-5 text-black outline-none xs:w-full xs:text-base md:w-[600px] lg:text-xl xl:w-[900px] xl:text-2xl ${
                error ? "border-red-500" : ""
              }`}
            />
            <div className="space-y-2">
              <p className="border " />
              {error && <p className="fixed text-red-500">{error}</p>}
            </div>
          </div>
        </>
      )}
      {step === 4 && (
        <>
          <div className="space-y-7">
            <div>
              <h1 className="font-bold text-black xs:text-xl md:text-2xl lg:text-4xl">
                {Strings.Q3}
              </h1>
            </div>
            <PhoneInput
              country={"in"}
              value={phoneNumber}
              onChange={(
                value: string,
                data: {} | CountryData,
                event: React.ChangeEvent<HTMLInputElement>,
                formattedValue: string
              ) => {
                setPhoneNumber(value);
                if (value.trim().length >= 10) setError("");
              }}
              inputClass="phoneinput"
            />
            <div className="space-y-2">
              <p className="border " />
              {error && <p className="fixed text-red-500">{error}</p>}
            </div>
          </div>
        </>
      )}
      {step === 5 && (
        <>
          <div className="space-y-7  ">
            <h1 className="font-bold text-black xs:text-xl md:text-2xl lg:text-4xl">
              {Strings.Q4}
            </h1>
            <input
              type="text"
              required
              value={position}
              onChange={(e) => {
                setPosition(e.target.value);
                setError("");
              }}
              placeholder="Enter position (eg. UIUX designer,Frontend developer ect.)"
              className={`rounded-lg border-[2px] p-2 py-5 text-black outline-none xs:w-full xs:text-base md:w-[600px] lg:text-xl xl:w-[900px] xl:text-2xl ${
                error ? "border-red-500" : ""
              }`}
            />
            <div className="space-y-2">
              <p className="border " />
              {error && <p className="fixed text-red-500">{error}</p>}
            </div>
          </div>
        </>
      )}
      {step === 6 && (
        <>
          <div className="space-y-7  ">
            <h1 className="font-bold text-black xs:text-xl md:text-2xl lg:text-4xl">
              {Strings.Q5}
            </h1>
            <input
              type="text"
              required
              value={experience}
              onChange={(e) => {
                setExperience(e.target.value);
                setError("");
              }}
              placeholder="Number of years"
              className={`rounded-lg border-[2px] p-2 py-5 text-black outline-none xs:w-full xs:text-base md:w-[600px] lg:text-xl xl:w-[900px] xl:text-2xl ${
                error ? "border-red-500" : ""
              }`}
            />
            <div className="space-y-2">
              <p className="border " />
              {error && <p className="fixed text-red-500">{error}</p>}
            </div>
          </div>
        </>
      )}
      {step === 7 && (
        <>
          <div className="space-y-7  ">
            <h1 className="font-bold text-black xs:text-xl md:text-2xl lg:text-4xl">
              {Strings.Q6}
            </h1>
            <input
              type="text"
              required
              value={linkedinURL}
              onChange={(e) => {
                setLinkedinURL(e.target.value);
                setError("");
              }}
              placeholder="Enter your Linkedin link here"
              className={`rounded-lg border-[2px] p-2 py-5 text-black outline-none xs:w-full xs:text-base md:w-[600px] lg:text-xl xl:w-[900px] xl:text-2xl ${
                error ? "border-red-500" : ""
              }`}
            />
            <div className="space-y-2">
              <p className="border " />
              {error && <p className="fixed text-red-500">{error}</p>}
            </div>
          </div>
        </>
      )}
      {step === 8 && (
        <div className="space-y-7 ">
          <h1 className="font-bold  text-black xs:text-xl md:text-2xl lg:text-4xl">
            {Strings.Q7}
          </h1>
          <div className="space-y-2">
            <FileUploader onFileSelect={setFile} />
            <p className="border" />
            {error && <p className="fixed text-red-500">{error}</p>}
          </div>
        </div>
      )}
      {step === 9 && (
        <div>
          <div className=" flex justify-center">
            <Image
              className=" h-[35px] w-[165px]"
              src={Images.REMOTEHIRELOGODark}
              alt="logo"
              height={50}
              width={50}
            />
          </div>
          <div className="mt-7 w-[650px] text-center">
            <h1 className="text-lg font-medium text-black">
              {Strings.This_test_designed_to_assess}
            </h1>
            <p className="my-5 text-lg font-medium text-black">
              {Strings.Please_note_that}
            </p>
          </div>
          <div className="flex justify-center">
            <button
              onClick={() => {
                setStep(step + 1);
              }}
              className="flex w-32 items-center justify-end rounded-full bg-[#8d3f42] p-2 text-lg text-white  hover:bg-[#161616] "
            >
              {Strings.Continue}
              <div className="ml-2 flex h-8 w-8 items-center justify-center rounded-full border-transparent bg-slate-500 bg-opacity-[20%]">
                <RightOutlined />
              </div>
            </button>
          </div>
        </div>
      )}
      {step === 10 && <Step10 setStep={setStep} />}
      {step === 11 && <Voicerecorder />}
      {step > 1 && step <= 8 && (
        <div className="mt-7 flex justify-end gap-x-4">
          {step !== 2 && (
            <button
              onClick={() => {
                setStep(step - 1);
              }}
              className="flex items-center justify-center gap-x-2 rounded-full bg-[#8d3f42] bg-opacity-[60%] px-2 py-2 text-lg text-white hover:bg-[#161616]"
            >
              <div className=" flex h-10 w-10 items-center justify-center rounded-full border-transparent bg-slate-500 bg-opacity-[20%]">
                <LeftOutlined />
              </div>
              {Strings.Previous}
            </button>
          )}
          <button
            onClick={step === 8 ? handleSubmit : handleNext}
            className="flex items-center justify-center gap-x-2 rounded-full bg-[#8d3f42] px-2 py-2 text-lg text-white hover:bg-[#161616]"
          >
            {step === 8 ? Strings.Submit : Strings.Next}
            <div className=" flex h-10 w-10 items-center justify-center rounded-full border-transparent bg-slate-500 bg-opacity-[20%]">
              {step === 8 ? <CheckOutlined /> : <RightOutlined />}
            </div>
          </button>
        </div>
      )}
    </div>
  );
};

export default Technicalform;

import {
  AudioOutlined,
  ClockCircleOutlined,
  PauseOutlined,
} from "@ant-design/icons";
import React, { useEffect, useRef, useState } from "react";

import { Line } from "rc-progress";
import PuffLoader from "react-spinners/PuffLoader";
import { Strings } from "@/constants";

const Voicerecorder = () => {
  const totalSteps = 11;
  const questions = [
    Strings.QUS1,
    Strings.QUS2,
    Strings.QUS3,
    Strings.QUS4,
    Strings.QUS5,
    Strings.QUS6,
    Strings.QUS7,
    Strings.QUS8,
    Strings.QUS9,
    Strings.QUS10,
  ];

  const [step, setStep] = useState<number>(1);
  const progress = (step / totalSteps) * 100;
  const [time, setTime] = useState(120);
  const [recording, setRecording] = useState(false);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [audioResponses, setAudioResponses] = useState<Array<Blob | null>>(
    Array(totalSteps).fill(null)
  );
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunks = useRef<BlobPart[]>([]);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const minutes = Math.floor(time / 60);
  const seconds = time % 60;

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      mediaRecorderRef.current = new MediaRecorder(stream);
      audioChunks.current = [];

      mediaRecorderRef.current.ondataavailable = (event) => {
        audioChunks.current.push(event.data);
      };

      mediaRecorderRef.current.onstop = () => {
        const blob = new Blob(audioChunks.current, { type: "audio/webm" });
        setAudioBlob(blob);
        const url = URL.createObjectURL(blob);
        setAudioUrl(url);
      };

      mediaRecorderRef.current.start();
      setRecording(true);
    } catch (err) {
      setError(`Error accessing microphone`);
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current) {
      mediaRecorderRef.current.stop();
      setRecording(false);
    }
  };

  const handleToggleRecording = () => {
    if (recording) {
      stopRecording();
      setError("");
    } else {
      startRecording();
      setError("");
    }
  };

  const handleSubmit = () => {
    if (recording) {
      setError("Please stop the recording before submitting.");
      return;
    }
    if (!audioBlob) {
      setError("Please record your response before proceeding.");
      return;
    }

    console.log(`Response for Question ${step}:`, audioBlob);

    const updatedResponses = [...audioResponses];
    updatedResponses[step - 1] = audioBlob;
    setAudioResponses(updatedResponses);

    if (step < totalSteps) {
      setStep(step + 1);
      resetAudioState();
    } else {
      console.log("All questions have been answered.");
      console.log("All responses:", audioResponses);
    }
  };
  const autoSubmitAndMoveToNextStep = () => {
    if (!audioBlob) {
      console.warn(`No audio recorded for Question ${step}.`);
    } else {
      const updatedResponses = [...audioResponses];
      updatedResponses[step - 1] = audioBlob;
      setAudioResponses(updatedResponses);
    }

    if (step < totalSteps) {
      setStep(step + 1);
      resetAudioState();
    } else {
      console.log("All questions have been answered.");
      console.log("All responses:", audioResponses);
    }
  };

  const resetAudioState = () => {
    setAudioBlob(null);
    setAudioUrl(null);
    setError(null);
  };

  useEffect(() => {
    startTimer();
    return () => clearTimer();
  }, [step]);

  const startTimer = () => {
    clearTimer();
    setTime(120);

    timerRef.current = setInterval(() => {
      setTime((prevTime) => {
        if (prevTime <= 1) {
          clearTimer();
          autoSubmitAndMoveToNextStep();
          return 0;
        }
        return prevTime - 1;
      });
    }, 1000);
  };

  const clearTimer = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  };

  return (
    <div>
      {step <= totalSteps && (
        <div>
          <div className="fixed right-7 top-7 flex items-center justify-center gap-x-2 rounded-full bg-[#8d3f42] bg-opacity-[20%] px-2 py-[2px] text-xl font-bold text-black">
            <ClockCircleOutlined className=" text-[#8d3f42]" />
            <p>{`${minutes}:${seconds < 10 ? "0" : ""}${seconds}`}</p>
          </div>

          <div className="flex flex-col items-center justify-center space-y-2 text-center text-lg font-bold text-black">
            <h1>
              {step}/{totalSteps}
            </h1>
            <Line
              percent={progress}
              strokeWidth={4}
              trailWidth={4}
              strokeColor="#22c55e"
              trailColor="#cbd5e1"
              className="w-40 rounded-lg"
            />
          </div>

          <div className="flex justify-center">
            <p className="my-5 w-[450px] text-center text-lg font-bold text-black">
              {questions[step - 1]}
            </p>
          </div>

          <div className="relative flex h-[250px] flex-col items-center justify-center rounded-2xl bg-[#8d3f42] bg-opacity-[20%] text-black md:w-[650px] lg:w-[950px] xl:w-[1200px]">
            <div className="relative flex items-center justify-center">
              {recording && (
                <PuffLoader
                  color="#8d3f42"
                  size={132}
                  speedMultiplier={1}
                  className="absolute"
                />
              )}
              <button
                onClick={handleToggleRecording}
                className="absolute flex h-16 w-16 items-center justify-center rounded-full bg-white text-2xl"
              >
                {recording ? (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                    className="h-7 w-7"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M6.75 5.25a.75.75 0 0 1 .75-.75H9a.75.75 0 0 1 .75.75v13.5a.75.75 0 0 1-.75.75H7.5a.75.75 0 0 1-.75-.75V5.25Zm7.5 0A.75.75 0 0 1 15 4.5h1.5a.75.75 0 0 1 .75.75v13.5a.75.75 0 0 1-.75.75H15a.75.75 0 0 1-.75-.75V5.25Z"
                      clip-rule="evenodd"
                    />
                  </svg>
                ) : (
                  <AudioOutlined className="text-[#000]" />
                )}
              </button>
            </div>
            <p
              onClick={handleToggleRecording}
              className={`absolute bottom-10 cursor-pointer font-bold`}
            >
              {recording
                ? "Recording has started..."
                : "Click to start recording"}
            </p>
          </div>

          {audioUrl && (
            <div className="my-5 flex justify-center">
              <audio controls src={audioUrl} className="w-[450px]" />
              <p className="mt-2 text-center font-bold text-black">
                Your recorded audio
              </p>
            </div>
          )}

          <div className="mb-7 mt-5 flex justify-center">
            <button
              onClick={handleSubmit}
              className="flex w-52 items-center justify-between rounded-full bg-[#8d3f42] bg-opacity-[60%] p-2 text-lg text-white hover:bg-[#161616]"
            >
              {Strings.Submit_continue}
              <div className="ml-2 flex items-center justify-center rounded-full border-transparent bg-slate-500 bg-opacity-[20%] xs:h-8 xs:w-8 md:h-10 md:w-10">
                <p className="rounded-sm bg-white p-2"></p>
              </div>
            </button>
          </div>
          <div className="flex flex-col items-center justify-center">
            {error && (
              <p className="fixed mb-11 text-center text-red-500">{error}</p>
            )}
            <p className="text-center font-medium text-slate-500">
              {Strings.Note}
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default Voicerecorder;

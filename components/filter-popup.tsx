import "react-datepicker/dist/react-datepicker.css";
import React, { useEffect, useRef, useState } from "react";
import { Strings } from "../constants";
import dropdownIcon from "@/public/assets/svg/dropdownIcon.svg";
import searchIcon from "@/public/assets/svg/searchIcon.svg";
import Image from "next/image";
import MultiSelectDropdown from "./MultiSelect";
import Chip from "./Chip";

type Skill = {
  label: string;
  value: string;
};

interface IFilter {
  closeModal: () => void;
  isOpen: any;
  advancedFilter: {
    technicalSkills: string[];
    softSkills: string[];
    country: string[];
    pricePerHour: number;
    availability: string;
  };
  setAdvancedFilter: (filter: any) => void;
  handleApplyFilter: () => void;
}

const Filtermodal: React.FC<IFilter> = ({
  isOpen,
  closeModal,
  advancedFilter,
  setAdvancedFilter,
  handleApplyFilter,
}) => {
  const techSkillRef = useRef<HTMLDivElement>(null);
  const [searchValue, setSearchValue] = useState("");
  const { technicalSkills, availability } = advancedFilter;
  const [value, setValue] = useState<number>(0);
  const [isOpenn, setIsOpenn] = useState(false);
  const toggleDropdown = () => {
    setIsOpenn(!isOpenn);
  };
  const handleSearchChange = (event: { target: { value: string } }) => {
    setSearchValue(event.target.value.toLowerCase());
  };
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }
  }, [isOpen]);

  const gradientBackground = `linear-gradient(to right, #8d3f42 0%, #8d3f42 ${parseInt(
    ((Number(value) / 100) * 100) as any
  )}%, #DEE2E6 ${parseInt(
    ((Number(value) / 100) * 100) as any
  )}%, #DEE2E6 100%)`;

  const updateSliderValue = (value: number) => {
    const sliderValue = document.getElementById("slider-value");
    if (sliderValue) {
      sliderValue.textContent = `$${value}/h`;
    }
  };

  const skillsData: Skill[] = [
    { label: "JavaScript", value: "Javascript" },
    { label: "React", value: "react" },
    { label: "Python", value: "python" },
    { label: "MongoDB", value: "mongodb" },
    { label: "GCP", value: "gcp" },
    { label: "Vue.js", value: "vue.js" },
    { label: "Kubernetes", value: "kubernetes" },
    { label: "3dcart", value: "3dcart" },
    { label: "ActionScript 3", value: "actionscript3" },
    { label: "Actix", value: "actix" },
    { label: "TypeScript", value: "typescript" },
    { label: "Node.js", value: "node.js" },
    { label: "Ruby", value: "ruby" },
    { label: "MySQL", value: "mysql" },
    { label: "AWS", value: "aws" },
    { label: "Angular", value: "angular" },
    { label: "GoLang", value: "golang" },
    { label: "Jenkins", value: "jenkins" },
    { label: "3DS Max", value: "3dsmax" },
    { label: "ActiveCampaign", value: "activecampaign" },
  ];

  const softSkillsData: Skill[] = [
    { label: "React", value: "react" },
    { label: "JavaScript", value: "javascript" },
    { label: "HTML", value: "html" },
    { label: "CSS", value: "css" },
  ];

  const countryData: Skill[] = [
    { label: "India", value: "India" },
    { label: "United States", value: "US" },
    { label: "Canada", value: "CA" },
    { label: "France", value: "FR" },
    { label: "Germany", value: "DE" },
  ];

  const handleSkillChange = (value: string): void => {
    setAdvancedFilter((prev: any) => {
      const technicalSkills = prev.technicalSkills.includes(value)
        ? prev.technicalSkills.filter((skill: string) => skill !== value)
        : [...prev.technicalSkills, value];
      return { ...prev, technicalSkills };
    });
  };

  const handleSoftSkillsChange = (selected: string[]) => {
    setAdvancedFilter((prev: any) => ({ ...prev, softSkills: selected }));
  };

  const handleCountryChange = (value: string[]): void => {
    setAdvancedFilter((prev: any) => ({ ...prev, country: value }));
  };

  // const handlePriceRangeChange = (event: { target: { value: string } }) => {
  //   const value = parseInt(event.target.value);
  //   setValue(value);
  //   updateSliderValue(value);
  //   setAdvancedFilter((prev: any) => ({ ...prev, pricePerHour: value }));
  // };

  const handlePriceRangeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(event.target.value);
    setValue(value);
    updateSliderValue(value);
    setAdvancedFilter((prev: any) => ({ ...prev, pricePerHour: value }));
  
    const inputElement = event.target as HTMLInputElement;
    const percentage = (value / 100) * 100;
    inputElement.style.setProperty("--progress", `${percentage}%`);
  };
  
  

  const getSkillLabel = (value: string): string => {
    const skill = skillsData.find((skill) => skill.value === value);
    return skill ? skill.label : "";
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (
      techSkillRef.current &&
      !techSkillRef.current.contains(event.target as Node)
    ) {
      setIsOpenn(false);
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleAvailabilityChange = (value: string) => {
    setAdvancedFilter((prev: any) => ({ ...prev, availability: value }));
  };

  return (
    <>
      {isOpen && (
        <div className="fixed left-0 top-0 z-50 flex h-full w-full  items-center justify-center  bg-black bg-opacity-[80%]">
          <div className=" relative min-h-[600px] rounded-2xl  bg-white   p-4 dark:bg-[#000] xs:w-[300px] md:w-[500px]">
            <div className="mb-[20px] text-[24px] font-bold text-[#000] dark:text-[white]">
              Filters
            </div>
            <div className="" ref={techSkillRef}>
              <div className="inline-block- relative">
                <label
                  htmlFor="countries"
                  className="mb-2 block text-sm font-medium text-gray-900 dark:text-white"
                >
                  {Strings.TECHNICAL_SKILLS}
                </label>
                <div
                  className="flex w-full cursor-pointer items-center justify-between rounded-[10px] border border-[#8D3F42] px-4 py-[14px] text-[16px]"
                  onClick={toggleDropdown}
                >
                  {technicalSkills.length > 0 ? (
                    <div className="flex flex-wrap gap-2">
                      {advancedFilter.technicalSkills.map((value) => {
                        return (
                          <Chip handleClick={() => handleSkillChange(value)} key={value}>
                            {getSkillLabel(value)}
                          </Chip>
                        );
                      })}
                    </div>
                  ) : (
                    "Select"
                  )}
                  <Image
                    className={`h-6 w-6 transform transition-transform ${
                      isOpenn ? "rotate-180" : ""
                    }`}
                    src={dropdownIcon}
                    alt="dropdownIcon"
                  />
                </div>

                <div
                  className={`absolute left-0 top-full z-20 mt-[10px] overflow-hidden rounded-[10px] bg-white py-4 w-full ${
                    isOpenn ? "block" : "hidden"
                  } shadow-[inset_0px_0px_20px_-12px_rgba(0,_0,_0,_0.8)]`}
                >
                  <div
                    className={`mb-[10px] flex h-[40px] flex-row items-center rounded-[10px] border border-[#8D3F42] bg-white mx-auto xs:w-[240px] md:w-[398px] ${
                      isOpenn ? "block" : "hidden"
                    } mt-[10px] w-full border-t  py-2`}
                  >
                    <Image
                      src={searchIcon}
                      alt="searchIcon"
                      className="ml-[10px] h-6 w-6"
                    />
                    <input
                      type="text"
                      placeholder="Search skills"
                      onChange={handleSearchChange}
                      className="outline-none"
                    />
                  </div>
                  <div
                    className={`grid grid-cols-2 ${
                      isOpenn ? "block" : "hidden"
                    } z-10 w-full ml-5`}
                  >
                    {skillsData
                      .filter((skill) =>
                        skill.label.toLowerCase().includes(searchValue)
                      )
                      .map((skill) => (
                        <label key={skill.value} className="px-4 py-2">
                          <input
                            type="checkbox"
                            className="mr-2"
                            checked={advancedFilter.technicalSkills.includes(
                              skill.value
                            )}
                            onChange={() => handleSkillChange(skill.value)}
                          />
                          {skill.label}
                        </label>
                      ))}
                  </div>
                </div>
              </div>
            </div>

            <MultiSelectDropdown
              label="Country"
              data={countryData}
              selectedValues={advancedFilter.country}
              onChange={handleCountryChange}
              placeholder="Choose country"
            />

            <div className="mt-4">
              <label
                htmlFor="link-checkbox"
                className="mb-2 block text-sm font-medium text-gray-900 dark:text-white"
              >
                {Strings.AVAILABILITY}
              </label>
              <div className="flex flex-col items-start gap-4 rounded-[10px] border border-[#8D3F42] px-[10px] py-[16px] text-[16px]">
                <label className="flex items-center gap-2">
                  <input
                    type="radio"
                    name="availability"
                    value="fullTime"
                    checked={availability === "fullTime"}
                    onChange={() => handleAvailabilityChange("fullTime")}
                    className="h-4 w-4 text-[#8D3F42]"
                  />
                  <span className="text-gray-900 dark:text-white">
                    Full Time
                  </span>
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="radio"
                    name="availability"
                    value="partTime"
                    checked={availability === "partTime"}
                    onChange={() => handleAvailabilityChange("partTime")}
                    className="h-4 w-4 text-[#8D3F42]"
                  />
                  <span className="text-gray-900 dark:text-white">
                    Part Time
                  </span>
                </label>
              </div>
            </div>

            <div className="mt-4">
              <div className="mb-[14px] text-[16px] font-bold text-[#000] dark:text-[white]">
                {Strings.PRICE_PER_HOUR}
              </div>
              <div className="slidecontainer">
                <input
                  id="myinput"
                  type="range"
                  min={0}
                  value={advancedFilter.pricePerHour}
                  max={100}
                  className="h-[1px] w-full appearance-none rounded-md bg-black outline-none custom-range"
                  placeholder="Price per hour"
                  onChange={handlePriceRangeChange}
                />
              </div>
              <div id="slider-value" className="slider-value">
                ${advancedFilter.pricePerHour}/h
              </div>
            </div>

            <div className="mt-[20px] text-right">
              <button
                onClick={() => {
                  handleApplyFilter();
                  closeModal();
                }}
                className=" rounded-[25px] bg-white px-[20px] py-2 font-semibold text-[#000] shadow-md outline-none dark:bg-[#8d3f42] dark:text-[#fff]"
                type="submit"
              >
                {Strings.Apply}
              </button>
            </div>

            <div className="absolute right-[10px] top-2">
                            <button
                title="Close"
                className="flex h-8 w-8 items-center justify-center rounded-full border-transparent bg-white text-black opacity-[80%] dark:bg-[#8d3f42]"
                onClick={closeModal}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth="1.5"
                  stroke="currentColor"
                  className="h-6 w-6 text-[#000] dark:text-[#fff] "
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default Filtermodal;

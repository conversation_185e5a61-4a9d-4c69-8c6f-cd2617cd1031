import { LeftOutlined, RightOutlined } from "@ant-design/icons";

import React, { useEffect, useState } from "react";
import ReactPaginate from "react-paginate";

interface PaginationProps {
  pageCount: number;
  handleChangePage: (page: number) => void;
  currentPage: number;
}

const Pagination: React.FC<PaginationProps> = ({
  pageCount,
  handleChangePage,
  currentPage,
}) => {
  const [isFirstTime, setIsFirstTime] = useState(true)

  return (
    <div>
      <ReactPaginate
        previousLabel={<LeftOutlined />}
        nextLabel={<RightOutlined />}
        pageCount={pageCount}
        onPageChange={({ selected }) => {
          if (isFirstTime) {
            setIsFirstTime(false)
            return
          }
          handleChangePage(selected);
        }}
        containerClassName="flex justify-end space-x-2 px-5 py-2"
        pageClassName="h-5 w-5 rounded-full flex justify-center items-center bg-[#fff] border border-[#8D3F42] hover:bg-[#8D3F42] text-[#8D3F42] hover:text-[#fff] text-[12px]"
        activeClassName="!bg-[#8D3F42] !text-white"
        previousClassName="h-5 w-5 rounded-full flex justify-center items-center bg-[#fff] border border-[#8D3F42] hover:bg-[#8D3F42] text-[#8D3F42] hover:text-[#fff] text-[12px]"
        nextClassName="h-5 w-5 rounded-full flex justify-center items-center bg-[#fff] border border-[#8D3F42] hover:bg-[#8D3F42] text-[#8D3F42] hover:text-[#fff] text-[12px]"
        breakLabel="..."
        breakClassName=""
        initialPage={currentPage}
        forcePage={currentPage}
      />
    </div>
  );
};

export default Pagination;

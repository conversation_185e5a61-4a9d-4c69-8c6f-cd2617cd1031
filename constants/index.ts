export const Images = {
  REMOTEHIRELOGO: "/Images/ERH_Vector_Logo_New.png",
  REMOTEHIRELOGODark: "/Images/ERH_Light_Logo.svg",
  RAHULSAHAI: "/Images/Men.png",
  BENIFITS: "/Images/benefits.png",
  STAR: "/Images/star.png",
  STOPWATCH: "/Images/Stopwatch Play.png",
  ARROWLONGTOSCO: "/Images/Arrow Right-tosco.png",
  ARROWLONGBLACK: "/Images/Arrow Right-black.png",
  PHONE: "/Images/Phone.png",
  MSG: "/Images/Letter.png",
  PHONE_WHITE: "/Images/Phone_white.png",
  MSG_WHITE: "/Images/Letter_white.png",
  FAV_ICON_BROWAN: "/Images/Brown-logo.webp",
  FAV_ICON_WHITE: "/Images/White-logo.webp",
  Aws: "/Images/aws.png",
  Python: "/Images/python.png",
  Typescript: "/Images/Typescript.png",
  javascrit: "/Images/javascript.svg",
  Reactnative: "/Images/react-native.png",
  Blockchain: "/Images/blockchain.png",
  Blockchain_Black: "/Images/blockchain_black.png",
  atom: "/Images/atom.webp",
  ReactSvg: "/Images/React.svg",
  JS: "/Images/JS.svg",
  Figma: "/Images/Figma.svg",
  checkIcon: "/Images/check.webp",
  TWITTER: "/Images/twitter.webp",
  INSTAGRAM: "/Images/Insta.webp",
  LINKDIN: "/Images/linkedin.webp",
  CLUTCH: "/Images/clutch.webp",
  GOOGAL: "/Images/google.webp",
  CHAIR: "/Images/chair.png",
  FAVOURITE: "/Images/favourite.png",
  LAPTOP: "/Images/laptop.png",
  MED_CAR: "/Images/medcar.png",
  MONEY_BAG: "/Images/money-bag.png",
  TREE: "/Images/tree.png",
  WORLD: "/Images/world.png",
  VERIFY: "/Images/verified 1.svg",
  PENCIL_LOGO: "/Images/pen-solid.svg",
  BARS_FILTER: "/Images/bars-filter.svg",
  dots: "/Images/dots.png",
  check: "/Images/check.png",
  checkmark: "/Images/checkmark.mp4",
  Scale: "/Images/Scale.png",
  Earlystage: "/Images/Earlystage.png",
  Enterprise: "/Images/Enterprise.png",
  Contacted: "/Images/Contacted.png",
  Linkedin: "/Images/linkedin.png",
  video: "/Images/video.mp4",
  PAYMENT_METHOD: "/Images/payment_method.svg",
  NOT_FOUND: "/Images/not_found.png",
  MONEY: "/Images/money.png",
  MONTHLY_PAYMENT: "/Images/monthly_payment.webp",
  Filter: "/Images/Filter.png",
  quickdamo: "/Images/quickdamo.png",
  Upgradeplane: "/Images/Upgradeplane.png",
  Copy: "/Images/Copy.png",
  NoRecords: "/Images/Norecords.png",
  AGENCY: "/Images/AGENCY.svg",
  FORWARD_ARROW: "/Images/forward_arrow.svg",
  ONBOARD: "/Images/Onboard.svg",
  Avtar: "/Images/Avtar.png",
  logo: "/Images/ERH_Vector_Logo_New.png",
  Checkicon: "/Images/checkicon.svg",
  Enter: "/Images/enter.svg",
  Play_icon: "Images/Play_icon.svg",
};
export const Strings = {
  BACK: "Back",
  DEVELOPERS: "Developers",
  DEVELOPER_DETAILS: "Developer details",
  REPLACMENT_REQUSET: "Replacement requested",
  GIVE_BOUNS: "Give bonus",
  GIVE_RAISE: "Give Raise",
  HIRE: "Hire a eremote Engineer",
  TOP: "Top 1% engineers, complience,payroll,and benifits on us. ",
  TOP_TALENT:
    "Any talent you've sourced yourself, use eremotehire COR to handle complience, payroll,benifits and more at a fixed $490/month",
  ONBOARD: "Onboard my own talent",
  QUS_1: " 1. What type of hire do you need?*",
  QUS_1_SUB: ` We offer full time (40 hours/week) and part time (20
    hours/week)`,
  FULL_TIME: "Full Time",
  PART_TIME: "Part Time",
  PLEASE_SELECT_AN_OPTION: "Please select an option.",
  QUS_2: "2. What skills should they have? (optional)",
  QUS_2_SUB: "If you don't know, that's okay. Just press next to skip.",
  CHOOSE_AS_MANY_AS_YOU_LIKE: "Choose as many as you like",
  ADD: "Add",
  QUS_3: "3. How many software engineers are you looking to hire?*",
  QUS_3_SUB: "You can start with 1 or 10. We have the talent ready!",
  QUS_4: "4. What’s your Full name?*",
  QUS_4_SUB: "Nice to meet you!",
  PLEASE_ENTER_YOUR_NAME: "Please enter name.",
  // QUS_5: " 5. What’s your last name?*",
  QUS_5: "5. What is your company email?*",
  PLEASE_ADD_YOUR_COMPANY_EMAIL: "Please add your company email",
  QUS_7: "7. Anything else you want to tell us?",
  FELL_FREE_TO_TELL:
    "Feel free to tell us about your company or anything else here",
  QUS_6: " 6. How many employees in your company?*",
  WE_TAILOR_OUR:
    "We tailor our solutions to fit your company’s specific needs.",
  SHIFT_ENTER: "Shift ⇧ + Enter ↵ to make a line break",
  QUS_8: " 8. Where did you find us?*",
  WE_APPRECIATE_IT: "We appreciate it!",
  SUBMIT: "Submit",
  THANK_YOU_FOR: "Thank you",
  THE_FORM: "The form was submitted successfully.",
  HIRE_AGENCY_THANK_YOU: "Your request to hire an agency has been submitted successfully.",
  HIRE_ENGINEER_THANK_YOU: "Your request to hire a engineer has been submitted successfully.",
  HIRE_A_TOP_ENGINEER: "Hire a Top Engineer",
  DASHBOARD: "Dashboard",
  HIRE_NEW_TALENT: "Hire New Talent",
  YOUR_ACCOUNT_MANAGER: "YOUR ACCOUNT MANAGER",
  HAND_PICKED_RECOMMENDATIONS: "Hand picked recommendations",
  HIRED_ENGINEERS: "HIRED ENGINEERS",
  DEVELOPER_NAME: "Mihir Mistry",
  EMAIL: "Email",
  CONTACT_NUMBER: "+91 99985 78704",
  DEVELOPERR: "Developer",
  ROLE: "Role ",
  AMOUNT: " Amount",
  SENT_ON: "Sent On",
  EFFECTIVE_ON: "Effective On",
  HOURS_WORKED: "HOURS WORKED",
  WORK_TYPE: "WORK TYPE",
  BOUNS_GIVEN: "BOUNS GIVEN",
  MONTHLY_SALARY: "MONTHLY SALARY",
  PERFORMANCE: "PERFORMANCE",
  WEEKLY_SUMMARIES: "WEEKLY SUMMARIES",
  RAISE: " Raise",
  BOUNS_HISTORY: "Bouns history",
  RAISE_HISTORY: "Raise history",
  BENEFITS: " Benefits",
  SETTINGS: "Settings",
  VETTING: "Vetting results",
  SEARCH_TALENT: "Search Talent",
  PARAGRAPH_SEARCH:
    "If you can't find the right profile, please click on 'get matched manually' and our team will match you with hand-picked profiles. The monthly rates you see are for full time (40h/week) hires.",
  GET_MANUALLY: "Get matched manually",
  SWITCH: "Switch to AI Search",
  BETA: " Beta",
  EXAMPLE: "EXAMPLE QUESTION",
  FIRST_EXAMPLE:
    "I am in search of a senior frontend developer with expertise in React and a knock for UI/UX design. Who in your talent pool fits this description?",
  SECOND_EXAMPLE:
    "Looking for a full-stack engineer who is Passionate about startups and can hit the ground running. Any matches?",
  AI_SEARCH: "AI Search",
  CLEAR_SEARCH: "Clear Search",
  AI_SUMMARY: "AI Summary:",
  NO_RESULTS_FOUND: "No results found",
  SWITCH_TO_AI: "Switch to AI Search",
  FILTERS: "Filters",
  SORT_BY: "Sort by rate",
  INTERVIEW_REQUEST: "Interview requested only",

  GPT_VETTING: "gpt-vetting",
  Customize_content: "Customize content",
  Manage_saved_tests: "Manage saved tests",
  Quick_demo: "Quick demo",
  Inviteacandidate: "Invite a candidate",
  Do_you_want:
    "Do you want to predefine the skills to test this candidate on ?",
  No_they_can_choose: "No, they can choose",
  yes_I_will_define_the_skills: "yes, I will define the skills",
  Please_define_the_skills: "Please define the skills",
  Save_as_a_new_test: "Save as a new test",
  Choose_from_existing_tests: "Choose from existing tests",
  Define_new_skill_set: "Define new skill sets",
  Level_of_difficulty: "Level of difficulty",
  Junior: "Junior",
  Mid_Level: "Mid Level",
  Senior: "Senior",
  Enter_the_name_email: "Enter the name & email address of the candidates",
  Please_enter_your_name: "Please enter your name",
  Enter_a_valid_email_address: "Enter a valid email address",
  Add_another_candidate: "+ Add another candidate",
  Add_coding_exercise: "Add coding exercise",
  Proctoring: "Proctoring",
  Send_invitation: "Send invitation",
  Reports: "Reports",
  Contected: "Contacted",
  Archived: "Archived",
  Filters: "Filters",
  Filter_by_test: "Filter by test",
  Select: "Select",
  Candidate_self_defined_skills: "Candidate self-defined skills",
  Date_taken: "Date taken",
  Apply: "Apply",
  Upgrade_plan: "Upgrade-plan",
  Upgrade_your_plane: "Upgrade your plan",
  Subscribe_to_a_plan: "Subscribe to a plan",
  Monthly: "Monthly",
  Yearly: "Yearly",
  Name: "Name",
  Test: "Test",

  Main_tech_stacks: "Main tech stacks",
  Soft_skills: "Soft skills",
  Proctoring_result: "Proctoring result",
  Edit: "Edit",
  Mark_as_contected: "Mark as contacted",
  Archive: "Archive",
  Are_You_sure: "Are you sure, you want to archive report of 'test' ?",
  Cancel: "Cancel",
  Yes_remove: "Yes,remove",
  Download_report: "Download report",
  POPUP_TEXT:
    " eRemote hire takes care of global payroll for your team, on your behalf. For you, global payroll simply becomes a stress-free subscription. We take care of the whole backend and compliance.",
  POPUP_TEXT_TWO:
    "Each engineer in our talent pool will go through a robust background check before working with you",

  TOTAL_MONTHLY_PAYROLL: "TOTAL MONTHLY PAYROLL",
  TOTAL_BONUS_GIVEN: "TOTAL BONUS GIVEN",
  Hire_New_Talent: "Hire New Talent",
  HIRED: "Hired",
  Recommendation: "Recommendation",
  VETTING_RESULT: "Vetting results",
  About: "About",
  Experience: "Experience",
  SKILL: "SKILL",
  YEAR_OF_EXPERIENCE: "YEAR OF EXPERIENCE",
  Background_checked: "Background checked",
  Read_More: "Read More",
  Read_Less: "Read Less",
  AWS: "AWS",
  Mid_level: "Mid level",
  React_js: "React.js",
  Project_management: "Project management",
  Start_test: "Start test",
  Note: "Note: please do not refresh the page or you'll lose the data.",
  IMPORTANT_POINTS: "IMPORTANT POINTS",
  Please_note:
    "Please note that this test is timed & the time will be defined per question.",
  We_encourage:
    "We encourage you to share your screen when prompted for transparency and proctoring.",
  You_cant:
    " You can't re-record the questions, please take a moment to think about your answer per question first.",
  Start_test_now: "Start test now",
  Test_your_microphone: "Test your microphone",
  Your_microphone: "Your microphone ?",
  Default_Macbook: "Default-Macbook Pro Microphone (Built-in)",
  Speak_and_pause: " Speak and pause do you hear a reply?",
  Input_level: "Input level",
  Speak: "Speak",
  Yes: "Yes",
  No: "No",
  Camera_microphone: "Camera & microphone access",
  You_re_about:
    "You're about to share your screen, give camera and microphone access. We only use this information to generate a trust score and prevent cheating.",
  Once_you_ve:
    "Once you've shared your screen and your camera is on, you can click on 'Record' and start answering your first question.",
  Best_of_luck: "Best of luck!",
  QUS1: " As a senior Node.js developer, can you explain the concept of event-driven programming and how it relates to Node.js? Provide an example of how you have uitized event-driven programming in your projects.",
  Submit_continue: "Submit & continue",
  QUS2: "Can you discuss the advantages and disadvantages of using callbacks versus promises in Node.js? Share on example of when you have used one approach over the other in your code and explain your reasoning behind the decision.",
  QUS3: "What are the different types of load balancers available in AWS and how would you choose the appropriate one for a give scenario?",
  QUS4: "Describe the concept of VPC peering in AWS and explain when and why it is used?",
  QUS5: "Explain the concept of state in React.js and how it is different from props.",
  QUS6: "What are React components and how would you create a function components in React.js?",
  QUS7: "Can you explain the difference between a project goal and a project objective?",
  QUS8: "How do you ensure effective communication among project team members?",
  QUS9: "Describe a time when you had to communicate a difficult message to a colleague or team member. How did you approach the situation and what was the outcome?",
  QUS10:
    "Tell me about a project or task that you were extremely passionate about. How did your intrinsic motivation impact your performance and the final outcome?",
  Coding_exercise: "Coding exercise",
  QUS11:
    "Given an array of integer,find the largest subarray that contains only add numbers. Return the length of the largest subarray. for example, given the array [2,3,5,7,8,10,11,12,12,13] the largest subarray that contains only add numbers is [3,5,7], with a length of 3.",
  EXAMPLE_1: "EXAMPLE 1",
  EXAMPLE_2: "EXAMPLE 2",
  Run_Code: "Run Code",
  Custom_input: "Custom input",
  Output: "Output",
  gpt_vetting_is_done: "gpt-vetting is done!",
  feedback: "Do you have any feedback on the test?",
  The_length_of:
    "1. The length of the input array is greater than 0 and less than 10000.",
  The_elements: "2. The elements in the array are integers.",
  The_array_can: "3. The array can contain positive and negative numbers.",
  The_array_can_contain: "4. The array can contain duplicate numbers.",
  The_largest:
    "The largest subarray that contains only add numbers is [7,9,-11,13], with a length of 4.",
  Requirements: "Requirements",

  Input: "Input:",
  array: "[2,3,5,7,8,10,11,12,12,13]",
  array1: "[-3,3,4,-5,7,9,-11,13]",
  Explanation: "Explanation:",
  The_largest_subarray:
    "The largest subarray that contains only add numbers is [3,5,7], with a length of 3.",
  Language: "Language:",
  JavaScript: "JavaScript",
  Python: "Python",
  C_PLUS: "C++",
  HTML: "HTML",
  CSS: "CSS",
  Manage_tests: "Manage tests",
  Add_new_test: "Add new test",
  Name_the_test: "Name the test",

  Save: "Save",

  Nothing_found: "Nothing found",
  Add: "Add",
  tooltip:
    "This adds 15 mins to the test and will test the candidate on a live coding exercise.",
  tooltip1:
    "Use video-based data and tab movements to generate a trust score and prevent cheating. We recommend keeping this on; however, you can turn off if there are any privacy concerns.",
  Your_logo: "Your logo",
  Company_logo: "Company logo",
  Welcome_screen: "Welcome screen",
  This_is_the: "This is the preview of the first screen of gpt-vetting",
  Edit_content: "Edit content",
  Invitation_email: "Invitation email",
  This_is_the_preview:
    "This is the preview of the invitation email a candidate gets.",
  CTA: "CTA",
  Best_wishes: "Best wishes,",
  if_you_experience: "if you experience any issues with the assessment,",
  reply_to_this_email: "reply to this email:",
  Upload_Video: "Upload Video",
  test_team: "test team",
  Dedicated_support: "Dedicated support",
  Custom_form: "Custom form",
  ATS_integrations: "ATS integrations",
  Custom_features: "Custom features",
  year: "/year",
  month: "/month",
  $: "$",
  No_records_found: "No records found",
  to: "to",
  Unarchive: "Unarchive",
  THANK_YOU_FOR_YOUR_INTEREST:
    "Thank you for your interest in your Company Name",
  Your_basic_details: "Your basic details",
  A_technical_test: "A technical test",
  Continue: "Continue",
  Q1: "1. What's your name ?",
  Name_Err: "Please enter your name.",
  Next: "Next",
  Q2: "2.What's your email ?",
  Email_err: "Please enter a valid email address.",
  Previous: "Previous",
  Q3: "3. What's your phone number ?",
  Phone_err: "Phone number must be at least 9 digits",
  Q4: "4. What position do you want apply for ?",
  Error: "This field is required.",
  Q5: "5. How many years of experience do you have ?",
  Q6: "6. What is your personal Linkedin URL ?",
  Q6_Err: "Invalid LinkedIn URL",
  Q7: "7. Upload your resume",
  Submit: "Submit",
  Remove: "Remove",
  Click_to_upload: "Click to upload",
  Select_files: "Select files",
  PDF_DOCX: "(.pdf,.docx)",
  This_test_designed_to_assess:
    " This test is designed to assess your technical skills. The difficulty of the questions are based on how you rate yourself on each skill.",
  Please_note_that:
    "Please note that this test is timed, with an approximate allocation of 2 minutes per question and the timer will start as soon as you see the first question",
  Pre_defined_skill_set: "Pre-defined skill set",
  Here_are_the_skills:
    " Here are the skills that will be the focus of your assessment.Expect two questions per skill, with each question allocated around 2 minutes for your response.",
  Main_skills: "Main skills",
  Rate_yourself: "Rate yourself",
  Node_js: "Node.js",
  AGENCY: "Hire an agency",
  EREMOTELAB: "Hire tech studio, a software development agency by eRemoteHire",
  FORM_AGENCY1: "Hire an agency, eRemoteLab",
  FORM_AGENCY2:
    "With eRemoteLab, eRemoteHire's in-house software development agency, you can develop any software quickly with a project-based engagement.",
  ABOUT_PROJECT: "Tell us about your project",
  AGENCY_QUE1: "1. What are you looking for?*",
  AGENCY_QUE1A: "We offer web development, app development, UI/UX",
  WEBSITE: "Website",
  APP: "App",
  UI_UX: "UI/UX",
  AGENCY_QUE2: "2. What is the project budget?*",
  AGENCY_QUE2A: "Just to get a rough understanding of budget.",
  Q2_OP1: "$15-50k",
  Q2_OP2: "$50k-200k",
  Q2_OP3: "$200k-500k",
  Q2_OP4: "$500k+",
  AGENCY_QUE3: "3. Describe your project*",
  AGENCY_QUE3A: "Feel free to describe it in 2-3 sentences. (Max. 500 words)",
  AGENCY_QUE4: "4. Would you like to attach any scope files or anything else?",
  DRAG: "Drag & Drop files or ",
  SELECT: "Select files",
  UPLOAD: " to upload",
  REMOVE: "Remove",
  ACCEPT: "I accept",
  TERMS: "Terms & conditions",
  FILL_TEXT: "Please fill this in",
  NOTHING_FOUND: "Nothing found",
  HANDPICKED_RECOMMENDATIONS: "No hand picked recommendations available",
  CREATE_REQUIREMENT:
    "Create your first requirement to receive hand-picked recommendations.",

  NO_HANDPICKED: "No hand picked",
  RECOMMENDATIONS: "recommendations available",
  CREATE_YOUR_REQUIREMENT: "Create your first requirement to receive",
  HAND_PICKED: "hand-picked recommendations.",
  NO_DATA_AVAILABLE: "No data available",
  EDUCATION: "Education",
  TECH_STACKS_USED: "Teck stacks used",
  RESPONSIBILITIES: "Responsibilities",
  PAYROLL: "Payroll",
  PAYROLL_AUTOPILOT: "payroll on autopilot",
  TIME_TRACKER: "Time Tracker",
  WEEKLY_SUMMARIE: "Weekly Summaries",
  TECHNICAL_SKILLS: "Technical skills",
  CHOOSE_SKILL: "Choose a skill",
  REACT: "React.js",
  COUNTRY: "Country",
  CHOOSE_COUNTRY: "Choose a country",
  UNITED_STATE: "United State",
  CANADA: "Canada",
  FRANCE: "France",
  GERMANY: "Germany",
  AVAILABILITY: "Availability",
  ONLY_VETTED_SKILLS: "Only consider vetted skills",
  PRICE_PER_HOUR: "Price per hour",
  HOW_MUCH_MIHIR: "How much raise do you want to give to Mihir?",
  WHEN_RAISE_EFFECTIVE: "When is this raise effective?",
  A: "A",
  B: "B",
  ASK_FOR_REPLACEMENT: "Ask for replacement",
  COMPLIANTLY_HIRED: "compliantly hired",
  GIVE_MORE_BONUS: "Give more bouns",
  THIS_MONTH: "This Month",
  NEXT_MONTH: "Next Month",
  WEEKLY_SUMMARY_DATA_BEYOND:
    "Weekly summeries data is only available from Dec 26th,2022 & beyond.",
  BENEFITS_SAKSHI: "Benefits Sakshi getting",
  WE_TAKE_CARE_DEV:
    " We take care of the developer's benefits so you don't have to.",
  NO_EXTA_FEES: " No extra fees for this.",
  VETTED_TECHNICAL_SKILL: "Vetted Technical Skill",
  THESE_ARE_SKILL:
    "These are the skill we have explicity vetted for in the technical interview",
  ABOUT_MIHIR: "About Mihir",
  NO_EDUCATION_DET: "No education details available",
  NO_EXP_DET: "No experience details available",
  REPORT_ID: "Report id: 007",
  PARTH_MAIL: "Email: <EMAIL>",
  TECHNICAL_RESULT: "TECHNICAL RESULTS",
  SELF_RATING_INTERMIDIATE: "Self rating intermediate",
  AI_ASSESMENT: "Al assessment:",
  GPT_PARA:
    "Roting by Al Date: 02 Aug 2023 The candidate's responses to the interview questions were unprofessional and disrespectful. They did not provide any relevant or meaningful information about event-driven programming in Node.js or how to handle memory leaks. Their lack of interest and knowledge in these areas indicates a low level of expertise in node.js. Based on their responses, the candidate can be rated as Not experienced.",
  EREMOTEHIRE: "eRemoteHire",
  POWERED_BY: "Powered by",
  SELF_RATING_BEGIN: "Self rating: beginner",
  RATING_BY_AI: "Rating by Al: Not experienced",
  RATING_AI_PARA:
    "The candidate has a beginner level of experience in React.js. They have a basic understanding of state management using useState and useEffect hooks. However, their explanation is not clear and lacks depth. They mention automating state changes using loops or timers, which is not the recommended approach in React.js. The candidate's explanation of the virtual DOM is also lacking clarity and does not demonstrate a strong understanding of the concept. Archive",
  ANY_TALENT_HIRE:
    "Any talent you've sourced yourself, use eremotehire COR to handle complience, payroll,benifits and more at a fixed $490/month.",
  C: "C",
  MY_TEAM: "My Team",
  NO_HIRES_MADE: "No hires made yet, click below to get",
  STARTED: "started",
  THESE_ARE_RECOMEND:
    "These are the recommendations based on your requirements",
  PAID_ON: "Paid On",
  NO_PAYMENT_FOUND: "No payment history found!",
  NO_BONUS_FOUND: "No bonus history found!",
  forgot_password: "Forgot Password?",
  if_you_dont_have:
    " if you don’t have an account, check your email for an invite",
  Are_you_a_dev: "Are you a Developer?",
  Login_here: "Login here",
  Password_must: " Password must be at least 6 characters",
  Email_is_invalid: "Email is invalid",
  Email_is_Required: " Email is Required",
  Back_to_login: "Back to login",
  PASSWORD_MUST_BE: "Password must be at least 6 characters",
  PASSWORDS_DO_NOT_MATCH: "Passwords do not match. Please try again.",
  LOGTN: "Login",
  OTP: "Enter OTP",
  PASSWORD: "Password",
  FORGOT_PASSWORD: "Forgot password",
  ENTER_YOUR_EMAIL:
    "Enter your email address and we'll send you a link to reset your password.",
  YOUR_EMAIL: "Your email",
  RESET_PASSWORD: "Reset Password",
  NEW_PASSWORD: "New Password",
  CONFIRM_NEW_PASSWORD: "Confirm New Password",
  AI_Ratings:"AI Ratings",
  OTHER_ENGINEER:"Other Engineer"
};

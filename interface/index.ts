export interface InterviewRequest {
  clientId: string;
  requestForInterview: boolean;
  requestedDate: string;
}

export interface EducationDetail {
  id: string;
  userId: string;
  course: string;
  university: string;
  department: string;
  startDate: string;
  endDate: string;
}

export interface ExperienceDetail {
  id: string;
  userId: string;
  companyName: string;
  techStack: string[];
  designation: string;
  responsibility: string;
  startDate: string;
  endDate: string;
  responsibilities: string[];
}

export interface Project {
  userId: string;
  projectName: string;
  startDate?: Date;
  endDate?: Date;
  techStack: string[];
  description: string;
  projectLink?: string;
  liveLink?: string;
  createdAt: Date;
  updatedAt: Date;
};

export interface ISubscription {
  planType: string;
  status: string;
}

export interface IUser {
  interviewRequest: InterviewRequest;
  id: string;
  userId: string;
  firstName: string;
  lastName: string;
  age: number;
  designation: string;
  techStack: string[];
  emailId: string;
  password: string;
  hourlyRate: number;
  country: string;
  address: string | null;
  phoneNo: number;
  yearOfExperience: number;
  profilePicture: string;
  resume: string;
  typeOfEngagement: string | null;
  currentStatus: string;
  noticePeriod: string | null;
  userRole: string;
  summary: string;
  managerID: string;
  certificate: string;
  client: string | null;
  technicalInterviewNotes: string | null;
  softSkillAssessment: string | null;
  verifiedAiTools: string[];
  hiredDate: string | null;
  socialLinkId: string;
  educationDetails: EducationDetail[];
  experienceDetails: ExperienceDetail[];
  projects: Project[];
  subscription: ISubscription | null;
}

export interface UserData {
  userData: any;
  softSkillAssessment:
    | string
    | number
    | boolean
    | readonly string[]
    | readonly number[]
    | readonly boolean[]
    | null
    | undefined;
  otherTechnicalSkills:
    | string
    | number
    | boolean
    | readonly string[]
    | readonly number[]
    | readonly boolean[]
    | null
    | undefined;
  verifiedAiTools:
    | string
    | number
    | boolean
    | readonly string[]
    | readonly number[]
    | readonly boolean[]
    | null
    | undefined;
  technicalInterviewNotes:
    | string
    | number
    | boolean
    | readonly string[]
    | readonly number[]
    | readonly boolean[]
    | null
    | undefined;
  monthlySalary: any;
  vettingResults: any;
  id: number;
  name: string;
  profile_process: string;
  postion: string;
  country: string;
  price: string;
  hiring_status: string;
  location: string;
  designation: string;
  rate: string;
  monthlyPayment: any;
  overview?: {
    workingHoursInDay: string;
    workType: string;
    monthlySalary: string;
    bonusGiven: string;
  };
  bonusHistory?: {
    developer: string;
    role: string;
    amount: string;
  };
  raiseHistory?: {
    developer: string;
    role_Software: string;
    raise: string;
  };
}

export interface Managing {
  profilePicture: any;
  firstName: any;
  lastName: any;
  emailId: any;
  phoneNo: any;
}
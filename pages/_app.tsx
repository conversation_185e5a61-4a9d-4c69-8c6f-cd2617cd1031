import FullScreenLoader from "@/components/Layouts/FullScreenLoader";
import { NextPage } from "next";
import type { AppProps } from "next/app";
import Head from "next/head";
import { useRouter } from "next/router";
import { appWithI18Next } from "ni18n";
import { ni18nConfig } from "ni18n.config.ts";
import { ReactElement, ReactNode, useEffect, useState } from "react";
import "react-perfect-scrollbar/dist/css/styles.css";
import { Provider } from "react-redux";
import DefaultLayout from "../components/Layouts/DefaultLayout";
import store from "../store/index";
import "../styles/tailwind.css";
import { jwtDecode } from "jwt-decode";
import Cookies from "js-cookie";

export type NextPageWithLayout<P = {}, IP = P> = NextPage<P, IP> & {
  getLayout?: (page: ReactElement) => ReactNode;
};

type AppPropsWithLayout = AppProps & {
  Component: NextPageWithLayout;
};

const App = ({ Component, pageProps }: AppPropsWithLayout) => {
  const [loading, setLoading] = useState<boolean>(false);
  const router = useRouter();
  const getLayout =
    Component.getLayout ?? ((page) => <DefaultLayout>{page}</DefaultLayout>);

  useEffect(() => {
    const handleStart = () => setLoading(true);
    const handleComplete = () => setLoading(false);

    router.events.on("routeChangeStart", handleStart);
    router.events.on("routeChangeComplete", handleComplete);
    router.events.on("routeChangeError", handleComplete);

    return () => {
      router.events.off("routeChangeStart", handleStart);
      router.events.off("routeChangeComplete", handleComplete);
      router.events.off("routeChangeError", handleComplete);
    };
  }, [router]);

  useEffect(() => {
    const publicRoutes = [
      "/auth/login",
      "/auth/register",
      "/auth/forgot-password",
      "/auth/reset-password",
    ];
    const accessToken = Cookies.get("token");
    let isValidate = false;
    if (accessToken) {
      const decodedToken: any = jwtDecode(accessToken);
      const currentTime = Date.now() / 1000;
      if (decodedToken.exp > currentTime) {
        isValidate = true;
      } else {
        isValidate = false;
        Cookies.remove("token");
        localStorage.removeItem("userData");
      }
    }

    if (!isValidate && !publicRoutes.includes(router.pathname)) {
      router.replace("/auth/login").then(() => router.reload());
    } else if (isValidate && publicRoutes.includes(router.pathname)) {
      router.replace("/").then(() => router.reload());
    }
  }, [router.pathname]);

  return (
    <Provider store={store}>
      <Head>
        <title>Dashboard | Client portal</title>
        <meta charSet="UTF-8" />
        <meta httpEquiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta name="description" content="Generated by create next app" />
        <link rel="icon" href="/favicon.png" />
      </Head>

      {loading ? <FullScreenLoader /> : getLayout(<Component {...pageProps} />)}
    </Provider>
  );
};

export default appWithI18Next(App, ni18nConfig);

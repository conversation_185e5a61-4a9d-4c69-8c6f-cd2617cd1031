// import "../app/globals.css";

import {
  <PERSON>RightOutlined,
  CopyOutlined,
  Dot<PERSON><PERSON>Outlined,
  DownOutlined,
  DownloadOutlined,
  MenuOutlined,
  SwapRightOutlined,
} from "@ant-design/icons";
// import { Images, Strings } from "@/constant";
import React, { useEffect, useRef, useState } from "react";

import { ArrowRightCircleIcon } from "@heroicons/react/24/outline";
import DOMPurify from 'dompurify';
import Image from "next/image";
import { Images } from "@/constants";
import axios from "axios";
import html2canvas from "html2canvas";
import { jsPDF } from "jspdf";
import { useRouter } from "next/router";

interface ReportData {
  id: string;
  userId: string | null;
  name: string | null;
  testTaken: string | null;
  dateTaken: string | null;
  techStack: string[];
  softSkills: string | null;
  proctoringResult: number | null;
  question: string[];
  answer: string[];
  video: string | null;
  success: boolean;
  skillRating: string | null;
  feedback: string | null;
  task: string | null;
  code: string | null;
  codeFeedback: string | null;
  email: string | null;
  frameCount: number | null;
  lookingAwayCount: number | null;
  multipleFacesCount: number | null;
  noFaceDetectedCount: number | null;
  tabSwitchCount: number | null;
  createdAt: string;
  updatedAt: string;
}

const AiInterviewReport = () => {
  const router = useRouter();

  if (!router.isReady) {
    return null;
  }

  const { id } = router.query;
  const [reportData, setReportData] = useState<ReportData | null>(null);
  const [loading, setLoading] = useState(true);
  const [isPlaying, setIsPlaying] = useState(false);
  const [transcript, setTranscript] = useState(false);
  const [videoURL, setVideoURL] = useState<string | null>(null);

  useEffect(() => {
    const fetchReportData = async () => {
      if (!id || typeof id !== "string") return;

      try {
        const response = await axios.get(
          `${process.env.NEXT_PUBLIC_API_URL}gptVetting/get`,
          {
            params: {
              id: id,
            },
          }
        );

        if (response.data && response.data.gptVettingData) {
          setReportData(response.data.gptVettingData);
          setVideoURL(response.data.gptVettingData.video);
        } else {
        }
      } catch (error) {
        console.error("Error fetching report data:", error);
      } finally {
        setLoading(false);
      }
    };

    if (router.isReady) {
      fetchReportData();
    }
  }, [id, router.isReady]);

  const toggleDropdown = () => {
    setTranscript(!transcript);
  };

  const handlePlay = () => {
    setIsPlaying(true);
  };

  const handlePause = () => {
    setIsPlaying(false);
  };

  if (loading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <div className="spinner"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!reportData) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <p className="text-xl font-semibold text-gray-800">No data found</p>
          <button
            onClick={() => router.push("/gpt-vetting")}
            className=" rounded-md bg-[#8d3f42] px-4 py-2 text-white hover:bg-opacity-90"
          >
            Back to List
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#F8F8F8] p-5">
      <div className="mx-auto max-w-[1440px]">
        <div className="mb-5 flex items-center justify-between">
          <h3 className="text-2xl font-bold text-black">AI Interview Report</h3>
          <button
            onClick={() => router.push("/gpt-vetting")}
            className="rounded-md bg-[#8d3f42] px-4 py-2 text-white hover:bg-opacity-90"
          >
            Back to List
          </button>
        </div>

        <section className="w-full rounded-lg bg-white p-5">
          <div className="flex items-center gap-x-5">
            <div className="w-full space-y-4">
              {/* Name */}
              <h3 className="text-2xl font-bold text-black">
                {reportData.name}
              </h3>

              {/* Tech Stack */}
              <div className="flex items-center gap-x-2">
                <h3 className="bg-tosca text-tosca rounded-md bg-opacity-30 p-2 text-sm font-normal">
                  Techstack :
                </h3>
                <h3 className="bg-tosca text-tosca rounded-md bg-opacity-30 p-2 text-sm font-normal">
                  {reportData.techStack.length > 0
                    ? reportData.techStack
                        .map((tech) =>
                          tech
                            .split(" ")
                            .map(
                              (word) =>
                                word.charAt(0).toUpperCase() +
                                word.slice(1).toLowerCase()
                            )
                            .join(" ")
                        )
                        .join(", ")
                    : "N/A"}
                </h3>
              </div>

              {/* Soft Skills */}
              <div className="flex items-center gap-x-2">
                <h3 className="bg-tosca text-tosca rounded-md bg-opacity-30 p-2 text-sm font-normal">
                  Soft Skills :
                </h3>
                <h3 className="bg-tosca text-tosca rounded-md bg-opacity-30 p-2 text-sm font-normal">
                    {reportData.techStack && reportData.techStack.length > 0
                      ? reportData.techStack
                          .map(
                            (tech: string) =>
                              tech.charAt(0).toUpperCase() + tech.slice(1).toLowerCase()
                          )
                          .join(', ')
                      : 'N/A'}
                </h3>
              </div>

              {/* Interview Status */}
              <div className="flex items-center gap-x-2">
                <h3 className="bg-tosca text-tosca rounded-md bg-opacity-30 p-2 text-sm font-normal">
                  Interview Status :
                </h3>
                <h3 className={`rounded-md p-2 text-sm font-normal ${
                  reportData.success 
                    ? "bg-green-100 text-green-600" 
                    : "bg-red-100 text-red-600"
                }`}>
                  {reportData.success ? "Completed" : "Incomplete"}
                </h3>
              </div>

              {/* Skill Rating */}
              {reportData.skillRating && (
                <div className="flex items-center gap-x-2">
                  <h3 className="bg-tosca text-tosca rounded-md bg-opacity-30 p-2 text-sm font-normal">
                    AI Rating :
                  </h3>
                  <h3 className={`rounded-md p-2 text-sm font-normal ${
                    reportData.skillRating === "Beginner"
                      ? "bg-red-200 text-red-600"
                      : reportData.skillRating === "Intermediate"
                      ? "bg-[#ECE2C1] text-[#CB9620]"
                      : "bg-[#daffdd] text-green-500"
                  }`}>
                    {reportData.skillRating}
                  </h3>
                </div>
              )}

              {/* Contact Information */}
              <div className="flex w-full items-center justify-between border-t pt-4 mt-4">
                <div className="flex items-center gap-x-2">
                  {reportData.email && reportData.email.length > 0 && (
                    <h3 className="rounded-md border bg-opacity-30 p-2 text-sm font-normal text-black">
                      {reportData.email}
                    </h3>
                  )}
                </div>
                <p className="text-xs lg:text-[14px] font-normal text-black">
                AI interview completed on {" "}
                  {reportData.createdAt
                    ? new Date(reportData.createdAt).toLocaleDateString(
                        "en-GB",
                        {
                          day: "2-digit",
                          month: "short",
                          year: "numeric",
                        }
                      )
                    : "N/A"}
                </p>
              </div>
            </div>
          </div>
        </section>

        <section className="my-5">
          <div className="space-y-5 rounded-lg bg-white p-5 text-black">
            <div className="flex items-center justify-between">
              <h3 className="text-xl font-semibold">AI interview recording</h3>
            </div>
            <div className="videoreport">
              <div className="bg-tosca relative rounded-xl bg-opacity-30 p-2">
                <div className="rounded-xl bg-black p-2 lg:p-4">
                  {videoURL ? (
                    <video
                      className="w-full rounded-xl object-cover"
                      controls
                      onPause={handlePause}
                      onPlay={() => setIsPlaying(true)}
                    >
                      <source src={videoURL} type="video/mp4" />
                    </video>
                  ) : (
                    <p className="mt-5 text-gray-500">
                      No recording available.
                    </p>
                  )}
                </div>
                {/* {!isPlaying && videoURL && (
                    <Image
                      src={Images.Play_icon}
                      alt="Play Icon"
                      height={80}
                      width={80}
                      onClick={handlePlay}
                      className="absolute left-1/2 top-1/2 h-20 w-20 -translate-x-1/2 -translate-y-1/2 transform cursor-pointer rounded-full bg-gradient-to-l from-[#481e1f] to-[#6b443a] xs:hidden lg:flex"
                    />
                  )} */}
              </div>
            </div>

            {/* Questions and Answers */}
            <div className="w-full">
              <div
                className={`flex ${
                  transcript
                    ? "rounded-t-lg border-x border-t"
                    : "rounded-lg border"
                } cursor-pointer items-center justify-between p-4`}
                onClick={toggleDropdown}
              >
                <h3 className="text-lg font-semibold">Questions & Answers</h3>
                <DownOutlined
                  className={`transition-transform duration-300 ease-in-out ${
                    transcript ? "rotate-180" : "duration-300"
                  }`}
                />
              </div>
              {transcript && (
                <div className="custom-scrollbar w-full space-y-8 overflow-y-scroll rounded-b-lg border bg-white p-4 text-justify text-sm font-medium">
                  {reportData.question.map((q, index) => (
                    <div key={index} className="mb-4">
                      <p className="mb-2 font-medium">
                        Q{index + 1}: {q}
                      </p>
                      {reportData.answer[index] && (
                        <audio controls className="w-full">
                          <source
                            src={reportData.answer[index]}
                            type="audio/mp3"
                          />
                          Your browser does not support the audio element.
                        </audio>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
            {reportData.feedback && (
              <div className="mt-5 w-full rounded-lg border bg-white p-4">
                <h2 className="mb-2 text-lg font-semibold">
                  AI Generated Feedback
                </h2>
                {/* <p className="text-sm text-gray-700">{reportData.feedback}</p> */}
                <div
                  className="text-sm text-gray-700"
                  dangerouslySetInnerHTML={{
                    __html: DOMPurify.sanitize(
                      reportData.feedback
                      // Add spacing and bold
                      .replace(/\*\*(.*?)\*\*/g, '<br /><br /><strong>$1</strong>')
                      // Convert line breaks
                      .replace(/\\r\\n|\\n|\\r/g, '<br />')
                      // Remove trailing asterisks (with or without space before)
                      .replace(/(\s?\*(.*?))<br \/>/g, '<br />')
                      .replace(/(\s?\*)$/, '') // in case last line ends with *
                  ),
                  }}
                />
              </div>
            )}
            {reportData.task && (
              <div className="mt-5 w-full rounded-lg border bg-white p-4">
                <h2 className="mb-2 text-lg font-semibold">Task</h2>
                {/* <p className="text-sm text-gray-700">{reportData.task}</p> */}
                <div
                  className="text-sm text-gray-700"
                  dangerouslySetInnerHTML={{
                    __html: DOMPurify.sanitize(
                      reportData.task
                        .replace(/^"(.*)"$/, '$1') // Remove starting/ending double quotes
                        .replace(/\\"/g, '"') // Unescape any \" quotes
                        .replace(/\\r\\n|\\n|\\r/g, '<br />') // Line breaks
                        .replace(/\* \(([^)]+)\)/g, '($1)') // Remove leading "*" before (number, number)
                        .replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>') // Make bold any text inside **
                    ),
                  }}
                />
              </div>
            )}
            {reportData.code && (
              <div className="mt-5- w-full rounded-lg border bg-white p-4"> 
                <h2 className="mb-2 text-lg font-semibold">Code</h2>
                {/* <p className="text-sm text-gray-700">{reportData.task}</p> */}
                <div className="mt-4 rounded bg-gray-100 p-3 text-sm text-gray-800 font-mono">
                  <div
                    dangerouslySetInnerHTML={{
                      __html: reportData.code.replace(/\\r\\n|\\n|\\r/g, '<br />'),
                    }}
                  />
                </div>
              </div>
            )}
            {/* {reportData.codeFeedback && (
              <div className="mt-5 w-full rounded-lg border bg-white p-4">
                <h2 className="mb-2 text-lg font-semibold">Code Feedback</h2>
                <p className="text-sm text-gray-700">
                  {reportData.codeFeedback}
                </p>
              </div>
            )} */}
            {reportData.codeFeedback && (
              <div className="mt-5 w-full rounded-lg border bg-white p-4">
                <h2 className="mb-2 text-lg font-semibold">Code Feedback</h2>
                <div
                  className="text-sm text-gray-700"
                  dangerouslySetInnerHTML={{
                    __html: DOMPurify.sanitize(
                      reportData.codeFeedback
                        // Add spacing and bold
                        .replace(/\*\*(.*?)\*\*/g, '<br /><br /><strong>$1</strong>')
                        // Convert line breaks
                        .replace(/\\r\\n|\\n|\\r/g, '<br />')
                        // Remove trailing asterisks (with or without space before)
                        .replace(/(\s?\*(.*?))<br \/>/g, '<br />')
                        .replace(/(\s?\*)$/, '') // in case last line ends with *
                    ),
                  }}
                />
              </div>
            )}
            <div className="mt-5 w-full rounded-lg border bg-white p-4">
              <div className="flex items-center justify-between mb-2">
                <h2 className="text-lg font-semibold">Proctoring Result</h2>
                <span className="text-lg font-bold text-green-600">
                  {reportData.proctoringResult ? `${reportData.proctoringResult}%` : 'N/A'}
                </span>
              </div>
              
              <p className="text-sm text-gray-600 mb-4 italic">
                This score is between 0 & 100% which is calculated using the duration of the violations that relate to cheating. For example, tab movements, eye movements, and more. The higher the score, the better.
              </p>

              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm text-gray-700">
                  <span>1. Total Frames</span>
                  <span className="font-medium">{reportData.frameCount || 0}</span>
                </div>
                <div className="flex items-center justify-between text-sm text-gray-700">
                  <span>2. Looking Away Frames</span>
                  <span className="font-medium">{reportData.lookingAwayCount || 0}</span>
                </div>
                <div className="flex items-center justify-between text-sm text-gray-700">
                  <span>3. Multiple Faces Frames</span>
                  <span className="font-medium">{reportData.multipleFacesCount || 0}</span>
                </div>
                <div className="flex items-center justify-between text-sm text-gray-700">
                  <span>4. No Face Detected Frames</span>
                  <span className="font-medium">{reportData.noFaceDetectedCount || 0}</span>
                </div>
                <div className="flex items-center justify-between text-sm text-gray-700">
                  <span>5. Tab Switching Count</span>
                  <span className="font-medium">{reportData?.tabSwitchCount || 0}</span>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

export async function getServerSideProps(context: any) {
  const { id } = context.params;

  if (!id) {
    return {
      notFound: true,
    };
  }

  return {
    props: {
      id,
    },
  };
}

export default AiInterviewReport;

import { AnimatePresence, motion } from "framer-motion";
import { Images, Strings } from "@/constants";
import React, { useEffect, useState } from "react";

import { ArrowLeftIcon } from "@heroicons/react/24/outline";
import Cookies from "js-cookie";
import FileUploader from "@/components/FileUploader";
import Image from "next/image";
import { Line } from "rc-progress";
import Link from "next/link";
import Loading from "@/components/Layouts/Loading";
import Swal from "sweetalert2";
import axios from "axios";

const formhireagency = () => {
  const worktypes = ["Website", "App", "UI/UX"];
  const budgets = ["$15-50k", "$50k-200k", "$200k-500k", "500k+"];
  const [error, setError] = useState("");
  const [selectedWorktypes, setSelectedWorktypes] = useState<string[]>([]);
  const [selectedBudget, setSelectedBudget] = useState<string | null>(null);
  const [activeModalPage, setActiveModalPage] = useState(1);
  const [message, setMessage] = useState("");
  const [file, setFile] = useState<any>([]);
  const progress = (activeModalPage / 5) * 100;
  const [isLoading, setIsLoading] = useState(false);
  let errorMessage = "";

  const toggleWorktype = (type: string) => {
    setSelectedWorktypes((prev) =>
      prev.includes(type) ? prev.filter((t) => t !== type) : [...prev, type]
    );
  };

  const SelectBudget = (budget: string) => {
    setSelectedBudget((prev) => (prev === budget ? null : budget));
  };

  const generateRandomId = () => {
    const randomId = "RH_0000012";
    return randomId.toString();
  };

  const handleFormSubmit = async () => {
    setIsLoading(true);
    const userId = generateRandomId();
    const formData = new FormData();
    formData.append("userId", userId);
    formData.append("projectType", selectedWorktypes.join(", "));
    formData.append("projectBudget", selectedBudget || "");
    formData.append("projectDescription", message);
    formData.append("files", file as Blob);

    try {
      const token = Cookies.get("token");
      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_API_URL}clientdashboard/addERemoteLabData`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "multipart/form-data",
          },
        }
      );
      if (response) {
        Swal.fire({
          toast: true,
          position: "top-end",
          showConfirmButton: false,
          timer: 3000,
          icon: "success",
          title:
            "Your request to hire an agency has been submitted successfully.",
          padding: "10px 20px",
          customClass: {
            popup: "swal-custom-toast",
          },
        });
        setActiveModalPage(activeModalPage + 1);
      }
    } catch (error) {
      console.log(error);
      Swal.fire({
        toast: true,
        position: "top-end",
        showConfirmButton: false,
        timer: 3000,
        icon: "error",
        title: "Something went wrong!",
        padding: "10px 20px",
        customClass: {
          popup: "swal-custom-toast",
        },
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleNext = () => {
    if (activeModalPage === 1 && selectedWorktypes.length === 0) {
      errorMessage = "Please select an option before proceeding.";
    } else if (activeModalPage === 2 && !selectedBudget) {
      errorMessage = "Please select a budget option before proceeding.";
    } else if (activeModalPage === 3 && message.trim() === "") {
      errorMessage = "Please fill in the project description.";
    } else if (activeModalPage === 4 && (!file || file.length === 0)) {
      errorMessage = "Please upload at least one file.";
    }

    if (errorMessage) {
      setError(errorMessage);
    } else {
      setError("");
      setActiveModalPage(activeModalPage + 1);
    }
  };

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Enter" && event.shiftKey) {
        return;
      }
      if (event.key === "Enter") {
        if (activeModalPage < 4) {
          event.preventDefault();
          handleNext();
        }
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [activeModalPage, selectedWorktypes, selectedBudget, message]);

  return (
    <div>
      <div
        className={`no-scrollbar relative flex h-[85vh] w-full items-center justify-center overflow-y-scroll rounded-xl bg-white p-5 text-[#000] shadow-[inset_0_0_4px_4px_rgba(0,0,0,0.1)] dark:bg-[#000] dark:text-white`}
      >
        <div className="absolute top-1 flex w-full items-center bg-transparent text-[#000] dark:text-white xs:h-16 md:h-20">
          {(activeModalPage === 1 || activeModalPage === 5) && (
            <Link href="/hire-new-talent">
              <button className="absolute top-5 flex h-10 w-20 transform items-center justify-center gap-x-2 rounded-md bg-[#8D3F42] text-sm text-white transition-transform hover:scale-110 xs:left-4 md:left-7">
                Exit
              </button>
            </Link>
          )}{" "}
          {activeModalPage > 1 && activeModalPage !== 5 && (
            <button
              onClick={() => {
                setActiveModalPage(activeModalPage - 1);
                setError("");
              }}
              className="absolute left-7 flex h-10 w-24 transform items-center justify-center gap-x-2 rounded-md bg-[#8D3F42] text-sm text-white transition-transform hover:scale-110 "
            >
              <ArrowLeftIcon className="h-5 w-5" />
              Back
            </button>
          )}
          <div className="absolute right-7 flex items-center gap-x-2">
            <Line
              percent={progress}
              strokeWidth={3}
              trailWidth={3}
              strokeColor="#009c20"
              trailColor="#122f17"
              className="w-40"
            />
            <h1>{activeModalPage}/5</h1>
          </div>
        </div>
        <div className="text-[#000] dark:text-white">
          <AnimatePresence mode="wait">
            {activeModalPage === 1 && (
              <motion.div
                key="page1"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.5, ease: "easeInOut" }}
              >
                <div className="gap-y-2">
                  <p className="font-outfit font-bold text-gray-900 dark:text-white xs:text-xl md:text-2xl lg:text-[24px]">
                    {Strings.AGENCY_QUE1}
                  </p>
                  <p className="font-outfit font-semibold text-[#7a7979] xs:text-lg md:text-xl lg:text-[16px]">
                    {Strings.AGENCY_QUE1A}
                  </p>
                </div>
                <div className="mt-4 flex gap-4">
                  {worktypes.map((type) => (
                    <button
                      key={type}
                      className={`relative rounded-lg border border-[#000] bg-white px-6 py-3 text-[#000] dark:border-[#fff] dark:bg-[#000] dark:text-white ${
                        selectedWorktypes.includes(type)
                          ? "border-opacity-100"
                          : "border-opacity-70"
                      }`}
                      onClick={() => {
                        toggleWorktype(type);
                        setError("");
                      }}
                    >
                      {type}
                      {selectedWorktypes.includes(type) && (
                        <Image
                          src={Images.Checkicon}
                          alt="Check icon"
                          height={500}
                          width={500}
                          className="absolute -right-1 -top-1 h-5 w-5"
                        />
                      )}
                    </button>
                  ))}
                </div>
                {error && <p className="fixed mt-2 text-red-500">{error}</p>}
              </motion.div>
            )}
            {activeModalPage === 2 && (
              <motion.div
                key="page2"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.5, ease: "easeInOut" }}
              >
                <div className="space-y-1">
                  <p className="font-outfit font-bold text-black dark:text-white xs:text-xl md:text-2xl lg:text-[24px]">
                    {Strings.AGENCY_QUE2}
                  </p>
                  <p className=" font-outfit font-semibold text-[#7a7979] xs:text-lg md:text-xl lg:text-[16px]">
                    {Strings.AGENCY_QUE2A}
                  </p>
                </div>

                <div className="mt-4 flex flex-wrap gap-4">
                  {budgets.map((value) => (
                    <button
                      key={value}
                      className={`relative rounded-lg border border-[#000] bg-white px-6 py-3 text-[#000] dark:border-[#fff] dark:bg-[#000] dark:text-white ${
                        selectedBudget === value
                          ? "border-opacity-100"
                          : "border-opacity-70"
                      }`}
                      onClick={() => {
                        SelectBudget(value);
                        setError("");
                      }}
                    >
                      {value}
                      {selectedBudget === value && (
                        <Image
                          src={Images.Checkicon}
                          alt="Check icon"
                          height={500}
                          width={500}
                          className="absolute -right-1 -top-1 h-5 w-5"
                        />
                      )}
                    </button>
                  ))}
                </div>
                {error && <p className="fixed mt-2 text-red-500">{error}</p>}
              </motion.div>
            )}
            {activeModalPage === 3 && (
              <motion.div
                key="page3"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.5, ease: "easeInOut" }}
              >
                <div className="gap-y-2">
                  <p className="font-outfit font-bold text-black dark:text-white xs:text-xl md:text-[24px]">
                    {Strings.AGENCY_QUE3}
                  </p>
                  <p className="font-outfit font-semibold text-[#7a7979]  xs:text-lg md:text-xl lg:text-[16px]">
                    {Strings.AGENCY_QUE3A}
                  </p>
                </div>
                <textarea
                  id="message"
                  rows={4}
                  className="border-Cod_Gray focus:ring-Cod_Gray focus:border-Cod_Gray dark:focus:ring-Cod_Gray dark:focus:border-Cod_Gray mt-4 block rounded-lg border-[1px] border-[#000] bg-white p-2.5 outline-none dark:border-white dark:bg-[#000] dark:text-white dark:placeholder-gray-400 xs:w-full xs:text-base md:w-[620px] lg:text-xl xl:w-[900px]"
                  placeholder="Type your answer here..."
                  name="Massage"
                  value={message}
                  onChange={(e) => {
                    setMessage(e.target.value);
                    setError("");
                  }}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" && e.shiftKey) {
                      e.preventDefault();
                      setMessage((prev) => `${prev}\n`);
                    }
                  }}
                />
                {error && <p className="fixed mt-2 text-red-500">{error}</p>}
                {/* <div className="mt-2 flex justify-between">
                {showWarning && (
                  <p className="fixed text-red-500 mt-2">
                    {Strings.FILL_TEXT}
                  </p>
                )}
                <p className="text-[#BC7666]w-full font-outfit">{`${wordsLeft} words left`}</p>
              </div> */}
              </motion.div>
            )}
            {activeModalPage === 4 && (
              <motion.div
                key="page4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.5, ease: "easeInOut" }}
              >
                <h1 className="mb-7 font-outfit font-bold text-black dark:text-white xs:text-xl md:text-[24px]">
                  {Strings.AGENCY_QUE4}
                </h1>
                <FileUploader onFileSelect={setFile} />
                {error && <p className="fixed mt-2 text-red-500">{error}</p>}
              </motion.div>
            )}
            {activeModalPage === 5 && (
              <motion.div
                key="page5"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.5, ease: "easeInOut" }}
              >
                <div className="flex justify-center">
                  <div className=" space-y-4 text-center">
                    <div className="bg-gradient-to-r from-[#8d3f42] to-[#bc7666] bg-clip-text font-outfit font-bold text-transparent xs:text-[30px] xl:text-6xl">
                      {Strings.THANK_YOU_FOR}
                    </div>
                    <div className="font-outfit font-semibold text-black dark:text-white xs:text-[25px] xl:text-3xl">
                      {Strings.HIRE_AGENCY_THANK_YOU}
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
        <div className="absolute bottom-0 flex w-full items-center justify-end bg-transparent px-7 xs:h-16 md:h-20">
          {activeModalPage === 4 ? (
            <button
              onClick={() => handleFormSubmit()}
              className="flex h-10 w-24 transform items-center justify-center gap-2 rounded-full bg-[#8d3f42] text-sm text-white transition-transform hover:scale-110 "
            >
              {isLoading ? <Loading /> : "Submit"}
            </button>
          ) : (
            <>
              {activeModalPage < 5 && (
                <>
                  <p className="mr-2 flex items-center bg-clip-text text-sm font-normal text-[#000] dark:text-white xs:hidden lg:flex">
                    Press enter
                    <Image
                      className="mx-1 h-4 w-4"
                      src={Images.Enter}
                      alt="Enter"
                      height={500}
                      width={500}
                    />
                    or
                  </p>
                  <button
                    onClick={handleNext}
                    className="flex h-10 w-24 transform items-center justify-center gap-2 rounded-full bg-[#8d3f42] text-sm text-[#fff] transition-transform hover:scale-110"
                  >
                    Next
                  </button>
                </>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};
export async function getServerSideProps(context: any) {
  const token = context.req.cookies.token;

  if (!token) {
    return {
      redirect: {
        destination: "/auth/login",
        permanent: false,
      },
    };
  }

  return { props: {} };
}
export default formhireagency;

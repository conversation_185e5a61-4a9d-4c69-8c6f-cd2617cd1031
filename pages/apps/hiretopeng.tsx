import { AnimatePresence, motion } from "framer-motion";
import { Images, Strings } from "@/constants";
import React, { useEffect, useState } from "react";

import { ArrowLeftIcon } from "@heroicons/react/24/outline";
import Cookies from "js-cookie";
import Image from "next/image";
import { Line } from "rc-progress";
import Link from "next/link";
import Loading from "@/components/Layouts/Loading";
import Optionmodal from "@/components/Optionmodal";
import Swal from "sweetalert2";
import axios from "axios";

// Skill normalization and fuzzy matching utilities
const normalizeSkill = (skill: string): string => {
  return skill
    .toLowerCase()
    .replace(/[^a-z0-9]/g, '') // Remove special characters and spaces
    .trim();
};

const calculateSimilarity = (str1: string, str2: string): number => {
  const longer = str1.length > str2.length ? str1 : str2;
  const shorter = str1.length > str2.length ? str2 : str1;

  if (longer.length === 0) return 1.0;

  const editDistance = levenshteinDistance(longer, shorter);
  return (longer.length - editDistance) / longer.length;
};

const levenshteinDistance = (str1: string, str2: string): number => {
  const matrix = [];

  for (let i = 0; i <= str2.length; i++) {
    matrix[i] = [i];
  }

  for (let j = 0; j <= str1.length; j++) {
    matrix[0][j] = j;
  }

  for (let i = 1; i <= str2.length; i++) {
    for (let j = 1; j <= str1.length; j++) {
      if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
        matrix[i][j] = matrix[i - 1][j - 1];
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j - 1] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j] + 1
        );
      }
    }
  }

  return matrix[str2.length][str1.length];
};

const formhireengineer = () => {
  const [worktype, setWorktype] = useState<string | null>(null);
  const [noOfSoftEngineer, setnoOfSoftEngineer] = useState<string | null>(null);
  const [noOfEmployee, setnoOfEmployee] = useState<string | null>(null);
  const [skills, setSkills] = useState<number[]>([]);
  const [findUs, setFindUs] = useState<number[]>([]);
  const [isOtherSelected, setIsOtherSelected] = useState(false);
  const [isOtherSelected2, setIsOtherSelected2] = useState(false);
  const [otherText, setOtherText] = useState("");
  const [otherText2, setOtherText2] = useState("");
  const [additionalOptions, setAdditionalOptions] = useState([]);
  const [additionalOptions2, setAdditionalOptions2] = useState([]);
  const [firstName, setfirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [companyEmail, setcompanyEmail] = useState("");
  const [optionCounter, setOptionCounter] = useState(10);
  const [optionCounter1, setOptionCounter1] = useState(7);
  const [message, setMessage] = useState("");
  const [activeModalPage, setActiveModalPage] = useState(1);
  const [selectedSkills, setSelectedSkills] = useState<string[]>([]);
  const [selectedFind, setSelectedFind] = useState<string[]>([]);
  const progress = (activeModalPage / 9) * 100;
  const [isLoading, setIsLoading] = useState(false);
  const [animating, setAnimating] = useState(false);
  const [error, setError] = useState("");
  const [skillSuggestion, setSkillSuggestion] = useState<string | null>(null);
  const [showSuggestion, setShowSuggestion] = useState(false);

  // Known skills database - add more as needed
  const knownSkills = [
    "ReactJS", "React.js", "React",
    "JavaScript", "Javascript", "JS",
    "Node.js", "NodeJS", "Node",
    "Express.js", "ExpressJS", "Express",
    "Python", "Python3",
    "React Native", "ReactNative",
    "AWS", "Amazon Web Services",
    "Blockchain", "Block Chain",
    "TypeScript", "Typescript", "TS",
    "Angular", "AngularJS", "Angular.js",
    "Vue.js", "VueJS", "Vue",
    "MongoDB", "Mongo DB", "Mongo",
    "PostgreSQL", "Postgres", "PostgresQL",
    "MySQL", "My SQL",
    "Docker", "Docker Container",
    "Kubernetes", "K8s",
    "GraphQL", "Graph QL",
    "Redux", "Redux.js",
    "Next.js", "NextJS", "Next",
    "Figma", "Figma Design"
  ];

  const findBestMatch = (inputSkill: string): string | null => {
    const normalizedInput = normalizeSkill(inputSkill);
    let bestMatch = null;
    let bestSimilarity = 0;
    const threshold = 0.7; // 70% similarity threshold

    for (const knownSkill of knownSkills) {
      const normalizedKnown = normalizeSkill(knownSkill);
      const similarity = calculateSimilarity(normalizedInput, normalizedKnown);

      if (similarity > bestSimilarity && similarity >= threshold) {
        bestSimilarity = similarity;
        bestMatch = knownSkill;
      }
    }

    return bestMatch;
  };
  const [optionModalData, setOptionModalData] = useState([
    {
      id: 1,
      buttonText: "Button 1 Text",
      label: "ReactJS",
      imageSrc: Images.ReactSvg,
    },
    {
      id: 2,
      buttonText: "Button 2 Text",
      label: "Figma",
      imageSrc: Images.Figma,
    },
    {
      id: 3,
      buttonText: "Button 2 Text",
      label: "Javascript",
      imageSrc: Images.javascrit,
    },
    {
      id: 4,
      buttonText: "Button 2 Text",
      label: "Node.Js",
      imageSrc: Images.JS,
    },
    {
      id: 5,
      buttonText: "Button 2 Text",
      label: "Python",
      imageSrc: Images.Python,
    },
    {
      id: 6,
      buttonText: "Button 2 Text",
      label: "React Native",
      imageSrc: Images.atom,
    },
    {
      id: 7,
      buttonText: "Button 2 Text",
      label: "AWS",
      imageSrc: Images.Aws,
    },
    {
      id: 8,
      buttonText: "Button 2 Text",
      label: "Blockchain",
      imageSrc: Images.Blockchain,
    },
    ...additionalOptions,
    {
      id: 9,
      buttonText: "Button 2 Text",
      label: "Other",
    },
  ]);
  const [optionModalData2, setOptionModalData2] = useState([
    {
      id: 1,
      buttonText: "Button 1 Text",
      label: "Twitter",
      imageSrc: Images.TWITTER,
    },
    {
      id: 2,
      buttonText: "Button 2 Text",
      label: "Linkedin",
      imageSrc: Images.Linkedin,
    },
    {
      id: 3,
      buttonText: "Button 2 Text",
      label: "Google",
      imageSrc: Images.GOOGAL,
    },
    {
      id: 4,
      buttonText: "Button 2 Text",
      label: "Clutch",
      imageSrc: Images.CLUTCH,
    },
    {
      id: 5,
      buttonText: "Button 2 Text",
      label: "Instagram",
      imageSrc: Images.INSTAGRAM,
    },
    ...additionalOptions2,
    {
      id: 6,
      buttonText: "Button 2 Text",
      label: "Other",
    },
  ]);

  const handleSubmit = async () => {
    try {
      setIsLoading(true);
      if (!findUs.length) {
        setError("Please let us know where did you find us.");
        setIsLoading(false);
        return;
      }
      const filteredSkills = selectedSkills.filter(
        (label) => label !== "Other"
      );
      const filteredFindUs = selectedFind.filter((label) => label !== "Other");

      const payload: Record<string, any> = {
        workType: worktype,
        noOfSoftEngineer: noOfSoftEngineer,
        firstName: firstName,
        lastName: lastName,
        companyEmail: companyEmail,
        noOfEmployee: noOfEmployee,
        findUs: filteredFindUs,
      };
      if (selectedSkills?.length) {
        payload.skill = filteredSkills;
      }
      if (message.trim()) {
        payload.message = message;
      }
      const data = JSON.stringify(payload);
      const token = Cookies.get("token");
      // console.log(data, "dataaaaaa");
      let config = {
        method: "post",
        maxBodyLength: Infinity,
        url: `${process.env.NEXT_PUBLIC_API_URL}hiretopengineer/addHireData`,
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        data: data,
      };
      const response = await axios.request(config);
      if (response) {
        Swal.fire({
          toast: true,
          position: "top-end",
          showConfirmButton: false,
          timer: 3000,
          icon: "success",
          title:
            "Your request to hire a engineer has been submitted successfully.",
          padding: "10px 20px",
          customClass: {
            popup: "swal-custom-toast",
          },
        });
        setActiveModalPage(activeModalPage + 1);
      }
    } catch (error) {
      console.log(error);
      Swal.fire({
        toast: true,
        position: "top-end",
        showConfirmButton: false,
        timer: 3000,
        icon: "error",
        title: "Something went wrong!",
        padding: "10px 20px",
        customClass: {
          popup: "swal-custom-toast",
        },
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleOptionClick = (data: { id: number; label: string }) => {
    let newSelectedOptions = [...skills];
    if (data.id === 9) {
      setIsOtherSelected(!isOtherSelected);
      if (!isOtherSelected) {
        newSelectedOptions = [...newSelectedOptions, data.id];
      } else {
        newSelectedOptions = newSelectedOptions.filter(
          (skill) => skill !== data.id
        );
      }
    } else {
      newSelectedOptions = newSelectedOptions.includes(data.id)
        ? newSelectedOptions.filter((skill) => skill !== data.id)
        : [...newSelectedOptions, data.id];
    }
    setSkills(newSelectedOptions);
    let newSelectedSkills = [...selectedSkills];
    if (data.id === 9) {
      if (!isOtherSelected) {
        newSelectedSkills = [...newSelectedSkills, data.label];
      } else {
        newSelectedSkills = newSelectedSkills.filter(
          (skill) => skill !== data.label
        );
      }
    } else {
      newSelectedSkills = newSelectedSkills.includes(data.label)
        ? newSelectedSkills.filter((skill) => skill !== data.label)
        : [...newSelectedSkills, data.label];
    }
    setSelectedSkills(newSelectedSkills);
  };

  const handleAddButtonClick = () => {
    if (isOtherSelected && otherText) {
      // Check for fuzzy match
      const bestMatch = findBestMatch(otherText);

      if (bestMatch && bestMatch.toLowerCase() !== otherText.toLowerCase()) {
        // Show suggestion
        setSkillSuggestion(bestMatch);
        setShowSuggestion(true);
        return;
      }

      // Use the original text or the exact match
      const skillToAdd = bestMatch || otherText;

      const newOption = {
        id: optionCounter,
        buttonText: "Button Text",
        label: skillToAdd,
      };
      setOptionCounter((prevCounter) => prevCounter + 1);
      const updatedOptionModalData = [
        ...optionModalData.slice(0, 8),
        newOption,
        ...optionModalData.slice(8),
      ];
      setOptionModalData(updatedOptionModalData);
      setSkills((prevSkills) => [...prevSkills, newOption.id]);
      setSelectedSkills((prevSelectedSkills) => [
        ...prevSelectedSkills,
        newOption.label,
      ]);
      setIsOtherSelected(false);
      setOtherText("");
      setShowSuggestion(false);
      setSkillSuggestion(null);
    }
  };

  const acceptSuggestion = () => {
    if (skillSuggestion) {
      const newOption = {
        id: optionCounter,
        buttonText: "Button Text",
        label: skillSuggestion,
      };
      setOptionCounter((prevCounter) => prevCounter + 1);
      const updatedOptionModalData = [
        ...optionModalData.slice(0, 8),
        newOption,
        ...optionModalData.slice(8),
      ];
      setOptionModalData(updatedOptionModalData);
      setSkills((prevSkills) => [...prevSkills, newOption.id]);
      setSelectedSkills((prevSelectedSkills) => [
        ...prevSelectedSkills,
        newOption.label,
      ]);
      setIsOtherSelected(false);
      setOtherText("");
      setShowSuggestion(false);
      setSkillSuggestion(null);
    }
  };

  const rejectSuggestion = () => {
    // Use original text
    const newOption = {
      id: optionCounter,
      buttonText: "Button Text",
      label: otherText,
    };
    setOptionCounter((prevCounter) => prevCounter + 1);
    const updatedOptionModalData = [
      ...optionModalData.slice(0, 8),
      newOption,
      ...optionModalData.slice(8),
    ];
    setOptionModalData(updatedOptionModalData);
    setSkills((prevSkills) => [...prevSkills, newOption.id]);
    setSelectedSkills((prevSelectedSkills) => [
      ...prevSelectedSkills,
      newOption.label,
    ]);
    setIsOtherSelected(false);
    setOtherText("");
    setShowSuggestion(false);
    setSkillSuggestion(null);
  };

  const handleOptionClick2 = (data: { id: number; label: string }) => {
    let newSelectedOptions2 = [...findUs];
    if (data.id === 6) {
      setIsOtherSelected2(!isOtherSelected2);
      if (!isOtherSelected2) {
        newSelectedOptions2 = [...newSelectedOptions2, data.id];
      } else {
        newSelectedOptions2 = newSelectedOptions2.filter(
          (findUs) => findUs !== data.id
        );
      }
    } else {
      newSelectedOptions2 = newSelectedOptions2.includes(data.id)
        ? newSelectedOptions2.filter((findUs) => findUs !== data.id)
        : [...newSelectedOptions2, data.id];
    }
    setFindUs(newSelectedOptions2);
    let newSelectedFind = [...selectedFind];
    if (data.id === 6) {
      if (!isOtherSelected) {
        newSelectedFind = [...newSelectedFind, data.label];
      } else {
        newSelectedFind = newSelectedFind.filter(
          (skill) => skill !== data.label
        );
      }
    } else {
      newSelectedFind = newSelectedFind.includes(data.label)
        ? newSelectedFind.filter((findUs) => findUs !== data.label)
        : [...newSelectedFind, data.label];
    }
    setSelectedFind(newSelectedFind);
  };

  const handleAddButtonClick2 = () => {
    if (isOtherSelected2 && otherText2) {
      const newOption = {
        id: optionCounter1,
        buttonText: "Button Text",
        label: otherText2,
      };
      setOptionCounter1((prevCounter) => prevCounter + 1);
      const updatedOptionModalData = [
        ...optionModalData2.slice(0, 5),
        newOption,
        ...optionModalData2.slice(5),
      ];
      setOptionModalData2(updatedOptionModalData);
      setFindUs((prevFindUs) => [...prevFindUs, newOption.id]);
      setSelectedFind((prevSelectedFindUs) => [
        ...prevSelectedFindUs,
        newOption.label,
      ]);
      setIsOtherSelected2(false);
      setOtherText2("");
    }
  };

  const handleNext = () => {
    let errorMessage = "";

    switch (activeModalPage) {
      case 1:
        if (!worktype) {
          errorMessage = "Please select an option before proceeding.";
        }
        break;
      case 3:
        if (!noOfSoftEngineer) {
          errorMessage = "Please select the number of software engineers.";
        }
        break;
      case 4:
        if (!firstName.trim() || !lastName.trim()) {
          errorMessage = "Please fill in both first and last name.";
        }
        break;
      case 5:
        if (
          !companyEmail.trim() ||
          !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(companyEmail)
        ) {
          errorMessage = "Please enter a valid company email address.";
        }
        break;
      case 6:
        if (!noOfEmployee) {
          errorMessage = "Please select the number of employees.";
        }
        break;
      case 8:
        if (!findUs.length && !isOtherSelected2) {
          errorMessage = "Please select an option or add your own.";
        } else if (isOtherSelected2 && !otherText2.trim()) {
          errorMessage =
            "Please fill in the 'Other' field or select an option.";
        }
        break;
      default:
        break;
    }

    if (errorMessage) {
      setError(errorMessage);
    } else {
      setError("");
      setActiveModalPage(activeModalPage + 1);
    }
  };

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Enter" && event.shiftKey) {
        return;
      }
      if (event.key === "Enter") {
        if (activeModalPage < 8) {
          event.preventDefault();
          handleNext();
        }
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [
    activeModalPage,
    worktype,
    noOfSoftEngineer,
    firstName,
    lastName,
    companyEmail,
    noOfEmployee,
    findUs,
    isOtherSelected2,
    otherText2,
  ]);

  const worktypes = [
    { label: "Part Time", value: "partTime" },
    { label: "Full Time", value: "fullTime" },
  ];
  const engineers = ["1-2", "2-5", "5+"];
  const employees = ["1-10", "10-50", "50+"];

  const selectWorkType = (worktype: string) => {
    setWorktype((prev) => (prev === worktype ? null : worktype));
  };

  const selectEngineer = (engineer: string) => {
    setnoOfSoftEngineer((prev) => (prev === engineer ? null : engineer));
  };

  const selectEmployees = (employee: string) => {
    setnoOfEmployee((prev) => (prev === employee ? null : employee));
  };

  return (
    <div>
      <div>
        <div
          className={`no-scrollbar relative flex h-full w-full items-center justify-center overflow-y-scroll rounded-xl bg-white p-5 text-[#000] shadow-[inset_0_0_4px_4px_rgba(0,0,0,0.1)] dark:bg-[#000] dark:text-white xs:h-[85vh]`}
        >
          <div className="absolute top-1 flex w-full items-center bg-transparent text-[#000] dark:text-white xs:h-16 md:h-20">
            {(activeModalPage === 1 || activeModalPage === 9) && (
              <Link href="/hire-new-talent">
                <button className="absolute top-5 flex h-10 w-20 transform items-center justify-center gap-x-2 rounded-md bg-[#8D3F42] text-sm text-white transition-transform hover:scale-110 xs:left-4 md:left-7">
                  Exit
                </button>
              </Link>
            )}
            {activeModalPage > 1 && activeModalPage !== 9 && (
              <button
                onClick={() => {
                  setActiveModalPage(activeModalPage - 1);
                  setError("");
                }}
                className="absolute flex h-10 w-24 transform items-center justify-center gap-x-2 rounded-md bg-[#8D3F42] text-sm text-white transition-transform hover:scale-110 xs:left-4 md:left-7 "
              >
                <ArrowLeftIcon className="h-5 w-5" />
                Back
              </button>
            )}
            <div className="absolute flex items-center gap-x-2 xs:right-4 md:right-7">
              <Line
                percent={progress}
                strokeWidth={3}
                trailWidth={3}
                strokeColor="#009c20"
                trailColor="#122f17"
                className="w-40"
              />
              <h1>{activeModalPage}/9</h1>
            </div>
          </div>
          <div className="text-[#000] dark:text-white">
            <AnimatePresence mode="wait">
              {activeModalPage === 1 && (
                <motion.div
                  key="page1"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.5, ease: "easeInOut" }}
                >
                  <div className="gap-y-2">
                    <p className="font-semibold text-[#000] dark:text-white xs:text-lg md:text-2xl">
                      {Strings.QUS_1}
                    </p>
                    <p className="font-normal text-[#000] dark:text-white xs:text-sm md:text-base">
                      {Strings.QUS_1_SUB}
                    </p>
                  </div>
                  <div className="mt-4 flex gap-4">
                    {worktypes.map(({ label, value }) => (
                      <button
                        key={value}
                        className={`relative rounded-lg border border-[#000] bg-white px-6 py-3 text-[#000] dark:border-[#fff] dark:bg-[#000] dark:text-white ${
                          worktype === value
                            ? "border-opacity-100"
                            : "border-opacity-70"
                        }`}
                        onClick={() => {
                          selectWorkType(value);
                          setError("");
                        }}
                      >
                        {label}
                        {worktype === value && (
                          <Image
                            src={Images.Checkicon}
                            alt="Check icon"
                            height={500}
                            width={500}
                            className="absolute -right-1 -top-1 h-5 w-5"
                          />
                        )}
                      </button>
                    ))}
                  </div>
                  {error && (
                    <p className="fixed mt-2 text-sm font-medium text-red-500">
                      {error}
                    </p>
                  )}
                </motion.div>
              )}
              {activeModalPage === 2 && (
                <motion.div
                  key="page2"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.5, ease: "easeInOut" }}
                >
                  <div className="gap-y-2">
                    <p className="font-semibold text-[#000] dark:text-white xs:text-lg md:text-2xl">
                      {Strings.QUS_2}
                    </p>
                    <p className="font-light text-[#000] dark:text-white xs:text-sm md:text-base">
                      {Strings.QUS_2_SUB}
                    </p>
                  </div>
                  <p className="my-2 text-xs font-light text-[#000] dark:text-white">
                    {Strings.CHOOSE_AS_MANY_AS_YOU_LIKE}
                  </p>
                  <div className="flex flex-wrap items-center gap-4 xs:w-80 md:w-[620px]">
                    {optionModalData.map((data) => (
                      <Optionmodal
                        isImageShow={data.id < 8.5 && data.id !== 9}
                        key={data.id}
                        buttonText={data.buttonText}
                        label={data.label}
                        imageSrc={data.imageSrc}
                        isSelected={
                          data.id === 9
                            ? isOtherSelected
                            : data.id > 8
                            ? true
                            : skills.includes(data.id)
                        }
                        onClick={() => handleOptionClick(data)}
                        imageClassName={
                          data.id === 6 || data.id === 8
                            ? "filter brightness-100 invert dark:filter dark:brightness-0 dark:invert"
                            : data.id === 7
                            ? "border border-[#000] dark:border-none"
                            : ""
                        }
                      />
                    ))}
                    {isOtherSelected && (
                      <div className="flex gap-x-4">
                        <input
                          type="text"
                          placeholder="Enter your other skills here..."
                          className="h-12 w-full rounded-lg border border-[#000] bg-[#fff] p-4 outline-none dark:border-[#fff] dark:bg-[#000] xs:text-sm lg:text-base"
                          value={otherText}
                          onChange={(e) => setOtherText(e.target.value)}
                        />
                        <button
                          onClick={handleAddButtonClick}
                          className="flex h-12 w-24 transform items-center justify-center rounded-lg border border-[#000] bg-[#fff] text-base text-[#000] transition-transform hover:scale-105 dark:border-[#fff] dark:bg-[#000] dark:text-white"
                        >
                          {Strings.ADD}
                        </button>
                      </div>
                    )}

                    {/* Skill Suggestion Modal */}
                    {showSuggestion && skillSuggestion && (
                      <div className="fixed left-0 top-0 z-50 flex h-full w-full items-center justify-center bg-gray-500 bg-opacity-[80%] backdrop-blur-sm">
                        <div className="relative mx-5 max-w-full rounded-lg bg-white p-6 xs:w-full md:w-[480px]">
                          <div className="space-y-4">
                            <h2 className="text-xl font-bold text-black">
                              Did you mean?
                            </h2>
                            <p className="text-gray-600">
                              We found a similar skill in our database:
                            </p>
                            <div className="rounded-lg bg-gray-100 p-4">
                              <p className="text-sm text-gray-600">You entered:</p>
                              <p className="font-semibold text-black">{otherText}</p>
                              <p className="mt-2 text-sm text-gray-600">Did you mean:</p>
                              <p className="font-semibold text-[#8d3f42]">{skillSuggestion}</p>
                            </div>
                            <div className="flex justify-end space-x-3">
                              <button
                                onClick={rejectSuggestion}
                                className="rounded-lg border border-gray-300 px-4 py-2 text-gray-600 hover:bg-gray-50"
                              >
                                No, use "{otherText}"
                              </button>
                              <button
                                onClick={acceptSuggestion}
                                className="rounded-lg bg-[#8d3f42] px-4 py-2 text-white hover:bg-[#7a3639]"
                              >
                                Yes, use "{skillSuggestion}"
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </motion.div>
              )}
              {activeModalPage === 3 && (
                <motion.div
                  key="page3"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.5, ease: "easeInOut" }}
                >
                  <div className="gap-y-2">
                    <p className="font-semibold text-[#000] dark:text-white xs:text-lg md:text-2xl">
                      {Strings.QUS_3}
                    </p>
                    <p className="font-normal text-[#000] dark:text-white xs:text-sm md:text-base">
                      {Strings.QUS_3_SUB}
                    </p>
                  </div>
                  <div className="mt-4 flex gap-4">
                    {engineers.map((engineers) => (
                      <button
                        key={engineers}
                        className={`relative rounded-lg border border-[#000] bg-white px-6 py-3 text-[#000] dark:border-[#fff] dark:bg-[#000] dark:text-white ${
                          noOfSoftEngineer === engineers
                            ? "border-opacity-100"
                            : "border-opacity-70"
                        }`}
                        onClick={() => {
                          selectEngineer(engineers);
                          setError("");
                        }}
                      >
                        {engineers}
                        {noOfSoftEngineer === engineers && (
                          <Image
                            src={Images.Checkicon}
                            alt="Check icon"
                            height={500}
                            width={500}
                            className="absolute -right-1 -top-1 h-5 w-5"
                          />
                        )}
                      </button>
                    ))}
                  </div>
                  {error && (
                    <p className="fixed mt-2 text-sm font-medium text-red-500">
                      {error}
                    </p>
                  )}
                </motion.div>
              )}
              {activeModalPage === 4 && (
                <motion.div
                  key="page4"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.5, ease: "easeInOut" }}
                >
                  <div className="w-full text-[#000] dark:text-white">
                    <div className="gap-y-2">
                      <p className="font-semibold text-[#000] dark:text-white xs:text-lg md:text-2xl">
                        {Strings.QUS_4}
                      </p>
                      <p className="font-normal text-[#000] dark:text-white xs:text-sm md:text-base">
                        {Strings.QUS_4_SUB}
                      </p>
                    </div>
                    <div className="flex xs:flex-col xs:gap-y-2 md:flex-row md:gap-x-4">
                      <input
                        type="text"
                        placeholder="Enter your first name"
                        className="mt-4 h-14 w-full rounded-lg border border-[#000] bg-[#fff] p-3 outline-none dark:border-[#fff] dark:bg-[#000] xs:text-sm lg:text-base"
                        value={firstName}
                        onChange={(e) => {
                          const cleanedValue = e.target.value.replace(
                            /[0-9]/g,
                            ""
                          );
                          setfirstName(cleanedValue);
                          setError("");
                        }}
                      />
                      <input
                        type="text"
                        placeholder="Enter your last name"
                        className="mt-4 h-14 w-full rounded-lg border border-[#000] bg-[#fff] p-3 outline-none dark:border-[#fff] dark:bg-[#000] xs:text-sm lg:text-base"
                        value={lastName}
                        onChange={(e) => {
                          const cleanedValue = e.target.value.replace(
                            /[0-9]/g,
                            ""
                          );
                          setLastName(cleanedValue);
                          setError("");
                        }}
                      />
                    </div>
                    {error && (
                      <p className="fixed mt-2 text-sm font-medium text-red-500">
                        {error}
                      </p>
                    )}
                  </div>
                </motion.div>
              )}
              {activeModalPage === 5 && (
                <motion.div
                  key="page5"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.5, ease: "easeInOut" }}
                >
                  <div className="gap-y-2">
                    <p className="font-semibold text-[#000] dark:text-white xs:text-lg md:text-2xl">
                      {Strings.QUS_5}
                    </p>
                    <p className="font-normal text-[#000] dark:text-white xs:text-sm md:text-base">
                      {Strings.PLEASE_ADD_YOUR_COMPANY_EMAIL}
                    </p>
                  </div>
                  <input
                    type="companyEmail"
                    placeholder="<EMAIL>"
                    className="mt-4 h-14 w-full rounded-lg border border-[#000] bg-[#fff] p-3 outline-none dark:border-[#fff] dark:bg-[#000] xs:text-sm lg:text-base"
                    value={companyEmail}
                    onChange={(e) => {
                      setcompanyEmail(e.target.value);
                      setError("");
                    }}
                  />
                  {error && (
                    <p className="fixed mt-2 text-sm font-medium text-red-500">
                      {error}
                    </p>
                  )}
                </motion.div>
              )}
              {activeModalPage === 6 && (
                <motion.div
                  key="page6"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.5, ease: "easeInOut" }}
                >
                  <div className="gap-y-2">
                    <p className="font-semibold text-[#000] dark:text-white xs:text-lg md:text-2xl">
                      {Strings.QUS_6}
                    </p>
                    <p className="font-normal text-[#000] dark:text-white xs:text-sm md:text-base">
                      {Strings.WE_TAILOR_OUR}
                    </p>
                  </div>
                  <div className="mt-4 flex gap-4">
                    {employees.map((employees) => (
                      <button
                        key={employees}
                        className={`relative rounded-lg border border-[#000] bg-white px-6 py-3 text-[#000] dark:border-[#fff] dark:bg-[#000] dark:text-white ${
                          noOfEmployee === employees
                            ? "border-opacity-100"
                            : "border-opacity-70"
                        }`}
                        onClick={() => {
                          selectEmployees(employees);
                          setError("");
                        }}
                      >
                        {employees}
                        {noOfEmployee === employees && (
                          <Image
                            src={Images.Checkicon}
                            alt="Check icon"
                            height={500}
                            width={500}
                            className="absolute -right-1 -top-1 h-5 w-5"
                          />
                        )}
                      </button>
                    ))}
                  </div>
                  {error && (
                    <p className="fixed mt-2 text-sm font-medium text-red-500">
                      {error}
                    </p>
                  )}
                </motion.div>
              )}
              {activeModalPage === 7 && (
                <motion.div
                  key="page7"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.5, ease: "easeInOut" }}
                >
                  <div className="gap-y-2">
                    <p className="font-semibold text-[#000] dark:text-white xs:text-lg md:text-2xl">
                      {Strings.QUS_7}
                    </p>
                    <p className="font-normal text-[#000] dark:text-white xs:text-sm md:text-base">
                      {Strings.FELL_FREE_TO_TELL}
                    </p>
                  </div>
                  <textarea
                    id="message"
                    rows={3}
                    className="mt-4 w-full rounded-lg border border-[#000] bg-[#fff] p-3 outline-none dark:border-[#fff] dark:bg-[#000] xs:text-sm lg:text-base"
                    placeholder="Type your answer here..."
                    name="Massage"
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === "Enter" && e.shiftKey) {
                        e.preventDefault();
                        setMessage((prev) => `${prev}\n`);
                      }
                    }}
                  ></textarea>
                  <p className="text-xs font-normal text-[#000] dark:text-white">
                    {Strings.SHIFT_ENTER}
                  </p>
                </motion.div>
              )}
              {activeModalPage === 8 && (
                <motion.div
                  key="page8"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.5, ease: "easeInOut" }}
                >
                  <div className="gap-y-2">
                    <p className="font-semibold text-[#000] dark:text-white xs:text-lg md:text-2xl">
                      {Strings.QUS_8}
                    </p>
                    <p className="font-normal text-[#000] dark:text-white xs:text-sm md:text-base">
                      {Strings.WE_APPRECIATE_IT}
                    </p>
                  </div>
                  <div className="mt-5 flex flex-wrap items-center gap-4 xs:w-80 md:w-[620px]">
                    {optionModalData2.map((data) => (
                      <Optionmodal
                        isImageShow={data.id < 5.5 && data.id !== 6}
                        key={data.id}
                        buttonText={data.buttonText}
                        label={data.label}
                        imageSrc={data.imageSrc}
                        isSelected={
                          data.id === 6
                            ? isOtherSelected2
                            : data.id > 5
                            ? true
                            : findUs.includes(data.id)
                        }
                        onClick={() => {
                          handleOptionClick2(data);
                          setError("");
                        }}
                      />
                    ))}
                    {isOtherSelected2 && (
                      <div className="flex gap-x-4">
                        <input
                          type="text"
                          placeholder="Enter your other skills here..."
                          className="h-12 w-full rounded-lg border border-[#000] bg-[#fff] p-4 outline-none dark:border-[#fff] dark:bg-[#000] xs:text-sm lg:text-base"
                          value={otherText2}
                          onChange={(e) => {
                            setOtherText2(e.target.value);
                            setError("");
                          }}
                        />
                        <button
                          onClick={handleAddButtonClick2}
                          className="flex h-12 w-24 transform items-center justify-center rounded-lg border border-[#000] bg-[#fff] text-base text-[#000] transition-transform hover:scale-105 dark:border-[#fff] dark:bg-[#000] dark:text-white"
                        >
                          {Strings.ADD}
                        </button>
                      </div>
                    )}
                  </div>
                  {error && (
                    <p className="fixed mt-2 text-sm font-medium text-red-500">
                      {error}
                    </p>
                  )}
                </motion.div>
              )}
              {activeModalPage === 9 && (
                <motion.div
                  key="page9"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.5, ease: "easeInOut" }}
                >
                  <div className="flex justify-center">
                    <div className=" space-y-4 text-center">
                      <div className="bg-gradient-to-r from-[#8d3f42] to-[#bc7666] bg-clip-text font-outfit font-bold text-transparent xs:text-[30px] xl:text-6xl">
                        {Strings.THANK_YOU_FOR}
                      </div>
                      <div className="font-outfit font-semibold text-black dark:text-white xs:text-[25px] xl:text-3xl">
                        {Strings.HIRE_ENGINEER_THANK_YOU}
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
          <div className="absolute bottom-0 flex w-full items-center justify-end bg-transparent px-7 xs:h-16 md:h-20">
            {activeModalPage === 8 ? (
              <button
                onClick={() => handleSubmit()}
                className="flex h-10 w-24 transform items-center justify-center gap-2 rounded-full bg-[#8d3f42] text-sm text-white transition-transform hover:scale-110 "
              >
                {isLoading ? <Loading /> : "Submit"}
              </button>
            ) : (
              <>
                {activeModalPage < 9 && (
                  <>
                    <p className="mr-2 flex items-center bg-clip-text text-sm font-normal text-[#000] dark:text-white xs:hidden lg:flex">
                      Press enter
                      <Image
                        className="mx-1 h-4 w-4"
                        src={Images.Enter}
                        alt="Enter"
                        height={500}
                        width={500}
                      />
                      or
                    </p>

                    <button
                      onClick={handleNext}
                      className="flex h-10 w-24 transform items-center justify-center gap-2 rounded-full bg-[#8d3f42] text-sm text-[#fff] transition-transform hover:scale-110"
                    >
                      Next
                    </button>
                  </>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export async function getServerSideProps(context: any) {
  const token = context.req.cookies.token;

  if (!token) {
    return {
      redirect: {
        destination: "/auth/login",
        permanent: false,
      },
    };
  }

  return { props: {} };
}

export default formhireengineer;

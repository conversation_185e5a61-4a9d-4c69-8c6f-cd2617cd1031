import { Images, Strings } from "@/constants";

import BlankLayout from "@/components/Layouts/BlankLayout";
import Image from "next/image";
import { LabelComponent } from "@/components/label";
import Link from "next/link";
import axios from "axios";
import { setEmailId } from "@/store/emailSlice";
import { toast } from "react-toastify";
import { useDispatch } from "react-redux";
import { useRouter } from "next/router";
import { useState } from "react";

const Forgotpassword = () => {
  const [email, setEmail] = useState("");
  const [error, setError] = useState<{ email: string }>({ email: "" });
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputEmail = e.target.value;
    setEmail(inputEmail);

    if (inputEmail.trim() === "") {
      setError({ email: "Email is required." });
    } else if (!validateEmail(inputEmail)) {
      setError({ email: "Please enter a valid email address." });
    } else {
      setError({ email: "" });
    }
  };

  const handleSubmit = async () => {
    if (email.trim() === "") {
      setError({ email: "Email is required." });
      return;
    }

    if (!validateEmail(email)) {
      setError({ email: "Please enter a valid email address." });
      return;
    }
    try {
      setLoading(true);
      console.log("Email:", email);

      await axios.post(
        `${process.env.NEXT_PUBLIC_API_URL}user/sendOTPforForgotPassword`,
        "",
        {
          params: {
            emailId: email,
          },
          headers: {
            accept: "*/*",
            "content-type": "application/x-www-form-urlencoded",
          },
        }
      );
      router.push("/auth/reset-password");
    } catch (error) {
      setLoading(false);
      toast.error("EmailID not exist or wrong EmailID");
    }
  };

  return (
    <section>
      <div className="mx-auto flex flex-col items-center justify-center px-6 py-8 xs:h-[90vh] md:h-screen lg:py-0">

        <div className="w-full rounded-lg bg-white shadow-[inset_0_0_4px_4px_rgba(0,0,0,0.1)] dark:border dark:border-gray-700 dark:bg-gray-800 sm:max-w-md md:mt-0 xl:p-0 ">
          <div className="space-y-4 p-6 sm:p-8 md:space-y-6  ">
            <LabelComponent
              label={Strings.FORGOT_PASSWORD}
              className="font-outfit text-xl font-bold md:text-2xl"
            />
            <LabelComponent
              label={Strings.ENTER_YOUR_EMAIL}
              className="font-outfit text-sm font-light text-gray-500 dark:text-gray-400"
            />

            <div>
              <LabelComponent
                label={Strings.YOUR_EMAIL}
                className="mb-2 block text-sm font-medium text-gray-900 dark:text-white"
              />
              <input
                type="email"
                name="email"
                id="email"
                value={email}
                onChange={handleEmailChange}
                className="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-gray-900 outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 sm:text-sm"
                placeholder="<EMAIL>"
                required
              />
              {error.email && (
                <p className="mt-2 text-xs font-light text-red-500">
                  {error.email}
                </p>
              )}
            </div>
            <button
              disabled={loading}
              onClick={handleSubmit}
              type="submit"
              className={`from-tosca to-contessa hover:from-contessa hover:to-tosca w-full rounded-full bg-black bg-gradient-to-l py-3
                     text-center font-outfit text-lg font-medium text-white hover:bg-gradient-to-l ${
                       loading ? "cursor-not-allowed opacity-50" : ""
                     }`}
            >
              {loading ? "Loading..." : Strings.RESET_PASSWORD}
            </button>
            <div className="flex items-center justify-end">
              <Link
                href="/auth/login"
                className="text-primary-600 dark:text-primary-500 mb-5 text-sm font-medium hover:underline"
              >
                {Strings.Back_to_login}
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
Forgotpassword.getLayout = (page: any) => {
  return <BlankLayout forceLightMode>{page}</BlankLayout>;
};
export default Forgotpassword;

import { EyeInvisibleOutlined, EyeOutlined } from "@ant-design/icons";
import { Images, Strings } from "@/constants";
import { useEffect, useState } from "react";

import BlankLayout from "@/components/Layouts/BlankLayout";
import Cookies from "js-cookie";
import Image from "next/image";
import Link from "next/link";
import axios from "axios";
import { error } from "console";
import { toast } from "react-toastify";
import { useRouter } from "next/router";

const LogInForm = () => {
  const router = useRouter();
  const [showpassword, setShowpassword] = useState(false);
  const [password, setPassword] = useState("");
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({ email: "", password: "" });

  const togglepasswordVisibility = () => {
    setShowpassword(!showpassword);
  };

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputEmail = e.target.value;
    setEmail(inputEmail);

    if (inputEmail.trim() === "") {
      setErrors((prev) => ({ ...prev, email: "Email is required." }));
    } else if (!validateEmail(inputEmail)) {
      setErrors((prev) => ({
        ...prev,
        email: "Please enter a valid email address.",
      }));
    } else {
      setErrors((prev) => ({ ...prev, email: "" }));
    }
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputPassword = e.target.value;
    setPassword(inputPassword);

    if (inputPassword.trim() === "") {
      setErrors((prev) => ({ ...prev, password: "Password is required." }));
    } else if (inputPassword.length < 6) {
      setErrors((prev) => ({
        ...prev,
        password: "Password must be at least 6 characters long.",
      }));
    } else {
      setErrors((prev) => ({ ...prev, password: "" }));
    }
  };

  const handleSubmit = async (e: any) => {
    if (email.trim() === "") {
      setErrors((prev) => ({ ...prev, email: "Email is required." }));
    }
    if (password.trim() === "") {
      setErrors((prev) => ({ ...prev, password: "Password is required." }));
    }

    if (
      errors.email ||
      errors.password ||
      email.trim() === "" ||
      password.trim() === ""
    ) {
      return;
    }

    try {
      setLoading(true);
      const data = JSON.stringify({
        emailId: email,
        password: password,
      });

      const config = {
        method: "post",
        maxBodyLength: Infinity,
        url: `${process.env.NEXT_PUBLIC_API_URL}user/login`,
        headers: {
          "Content-Type": "application/json",
        },
        data: data,
      };

      const response = await axios(config);
      console.log(response, "response");
      Cookies.set("token", response?.data?.signInData?.access_token);
      Cookies.set("userId", response?.data?.signInData?.userData?.userId);
      localStorage.setItem(
        "userData",
        JSON.stringify(response?.data?.signInData?.userData)
      );
      toast.success(response?.data?.message);
      router.push("/");
    } catch (error: any) {
      console.error(error);
      toast.error(
        error?.response?.data?.message || "Login failed. Please try again."
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <section className="bg-[#FFFF] ">
        <div className="mx-auto flex flex-col  items-center justify-center px-2 py-8 xs:h-[90vh] md:h-screen lg:py-0">
          <div className="h-[300px]- w-full rounded-lg bg-white shadow-[inset_0_0_4px_4px_rgba(0,0,0,0.1)] dark:border dark:border-gray-700 dark:bg-gray-800 sm:max-w-md md:mt-0 xl:p-0 ">
            <div className="md:space-y-6- mx-[5px] space-y-2 p-3 sm:p-8">
              <div className="flex items-center justify-between">
                <h1 className="text-2xl font-bold">{Strings.LOGTN}</h1>
                <Link href="/auth/register">
                  <h1 className="bg-gradient-to-l from-[#bc7666] to-[#8D3F42] bg-clip-text text-base font-semibold text-transparent">
                    Register
                  </h1>
                </Link>
              </div>
              <div>
                <div className="mb-2 block text-sm font-medium text-gray-900 dark:text-white">
                  {Strings.EMAIL}
                </div>
                <input
                  type="email"
                  name="email"
                  id="email"
                  value={email}
                  onChange={handleEmailChange}
                  className="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-gray-900 outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 sm:text-sm"
                  placeholder="<EMAIL>"
                  required
                />
                {errors.email && (
                  <p className="mt-2 font-outfit text-xs font-light text-red-500">
                    {errors.email}
                  </p>
                )}
              </div>
              <div className="relative">
                <div className="mb-2 block text-sm font-medium text-gray-900 dark:text-white">
                  {Strings.PASSWORD}
                </div>
                <input
                  type={showpassword ? "text" : "password"}
                  value={password}
                  onChange={handlePasswordChange}
                  name="password"
                  id="password"
                  placeholder="Password"
                  required
                  className="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-gray-900 outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 sm:text-sm"
                />
                {errors.password && (
                  <p className="mt-2 font-outfit text-xs font-light text-red-500">
                    {errors.password}
                  </p>
                )}
                <span
                  className="absolute right-3 top-[50px] flex -translate-y-1/2 transform cursor-pointer items-center text-lg"
                  onClick={togglepasswordVisibility}
                >
                  {showpassword ? <EyeInvisibleOutlined /> : <EyeOutlined />}
                </span>
              </div>
              <div className="flex items-center justify-end">
                <Link
                  href="/auth/forgot-password"
                  className="text-primary-600 dark:text-primary-500 mb-5 text-sm font-medium hover:underline"
                >
                  {Strings.forgot_password}
                </Link>
              </div>

              <button
                onClick={handleSubmit}
                type="submit"
                className={`from-tosca to-contessa hover:from-contessa hover:to-tosca w-full rounded-full bg-black bg-gradient-to-l py-3 text-center font-outfit text-lg font-medium text-white hover:bg-gradient-to-l ${
                  loading ? "cursor-not-allowed opacity-50" : ""
                }`}
                disabled={loading}
              >
                {loading ? "Loading..." : Strings.LOGTN}
              </button>

              <div className="flex justify-center">
                <p className="font-outfit text-sm font-light text-gray-500 dark:text-gray-400">
                  {Strings.if_you_dont_have}
                </p>
              </div>
            </div>
          </div>
          <div className="flex justify-center py-4 text-black">
            <p>{Strings.Are_you_a_dev} </p>
            <Link href={process.env.NEXT_PUBLIC_DEVELOPER_URL || "#"}>
              <button className="text-contessa ml-[5px] cursor-pointer underline">
                {Strings.Login_here}
              </button>
            </Link>
          </div>
        </div>
      </section>
    </>
  );
};

LogInForm.getLayout = (page: any) => {
  return <BlankLayout forceLightMode>{page}</BlankLayout>;
};

export async function getServerSideProps(context: any) {
  const token = context.req.cookies.token;

  if (token) {
    return {
      redirect: {
        destination: "/",
        permanent: false,
      },
    };
  }

  return { props: {} };
}

export default LogInForm;

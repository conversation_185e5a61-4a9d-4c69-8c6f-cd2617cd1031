import { ChangeEvent, FormEvent, useEffect, useState } from "react";
import { EyeInvisibleOutlined, EyeOutlined } from "@ant-design/icons";
import { Images, Strings } from "@/constants";

import BlankLayout from "@/components/Layouts/BlankLayout";
import Cookies from "js-cookie";
import Image from "next/image";
import Link from "next/link";
import axios from "axios";
import { error } from "console";
import { toast } from "react-toastify";
import { useRouter } from "next/router";

const Register = () => {
  const router = useRouter();
  const [rememberMe, setRememberMe] = useState(false);
  const [passwordError, setPasswordError] = useState<string | null>(null);
  const [confirmPasswordError, setConfirmPasswordError] = useState<
    string | null
  >(null);
  const [email, setEmail] = useState("");
  const [errors, setErrors] = useState({ email: "", password: "" });
  const [showpassword, setShowpassword] = useState(false);
  const [showpassword1, setShowpassword1] = useState(false);
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [confirmpassword, setConfirmPassword] = useState("");
  const [error, setError] = useState("");
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [companyName, setCompanyName] = useState("");

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleFirstNameChange = (e: React.ChangeEvent<HTMLInputElement>) =>
    setFirstName(e.target.value);
  const handleLastNameChange = (e: React.ChangeEvent<HTMLInputElement>) =>
    setLastName(e.target.value);
  const handleCompanyNameChange = (e: React.ChangeEvent<HTMLInputElement>) =>
    setCompanyName(e.target.value);

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputEmail = e.target.value;
    setEmail(inputEmail);

    if (inputEmail.trim() === "") {
      setErrors((prev) => ({ ...prev, email: "Email is required." }));
    } else if (!validateEmail(inputEmail)) {
      setErrors((prev) => ({
        ...prev,
        email: "Please enter a valid email address.",
      }));
    } else {
      setErrors((prev) => ({ ...prev, email: "" }));
    }
  };

  const handleSubmit = async (e: any) => {
    if (email.trim() === "") {
      setErrors((prev) => ({ ...prev, email: "Email is required." }));
    }
    if (password.trim() === "") {
      setErrors((prev) => ({ ...prev, password: "Password is required." }));
    }

    if (
      errors.email ||
      errors.password ||
      email.trim() === "" ||
      password.trim() === ""
    ) {
      return;
    }

    try {
      setLoading(true);
      const data = JSON.stringify({
        firstName,
        lastName,
        emailId: email,
        password,
        companyName,
      });

      const config = {
        method: "post",
        maxBodyLength: Infinity,
        url: `${process.env.NEXT_PUBLIC_API_URL}user/register`,
        headers: {
          "Content-Type": "application/json",
        },
        data: data,
      };

      const response = await axios(config);

      toast.success(response?.data?.message);
      router.push("/auth/login");
    } catch (error: any) {
      console.error(error);
      toast.error(
        error?.response?.data?.message || "Regiter failed. Please try again."
      );
    } finally {
      setLoading(false);
    }
  };
  const togglepasswordVisibility = () => {
    setShowpassword(!showpassword);
  };
  const togglepasswordVisibility1 = () => {
    setShowpassword1(!showpassword1);
  };
  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPassword(e.target.value);
    if (e.target.value.length < 6) {
      setPasswordError("Password must be at least 6 characters long.");
    } else {
      setPasswordError(null);
    }
  };

  const handleConfirmPasswordChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setConfirmPassword(e.target.value);
    if (e.target.value !== password) {
      setConfirmPasswordError("Please confirm your password.");
    } else {
      setConfirmPasswordError(null);
    }
  };

  return (
    <>
      <section className="bg-[#FFFF] ">
        <div className="mx-auto flex flex-col items-center justify-center px-2 py-8 xs:h-full md:h-screen lg:py-0">
          <div className="h-[300px]- w-full rounded-lg bg-white shadow-[inset_0_0_4px_4px_rgba(0,0,0,0.1)] dark:border dark:border-gray-700 dark:bg-gray-800 sm:max-w-md md:mt-0 xl:p-0 ">
            <div className="md:space-y-6- mx-[5px] space-y-2 p-3 sm:p-8">
              <div className="flex items-center justify-between">
                <h1 className="text-2xl font-bold">Register</h1>
                <Link href="/auth/login">
                  <h1 className="bg-gradient-to-l from-[#bc7666] to-[#8D3F42] bg-clip-text text-base font-semibold text-transparent">
                    Login
                  </h1>
                </Link>
              </div>
              <div className="flex w-full xs:flex-col xs:space-y-2 md:flex-row md:gap-x-2 md:space-y-0">
                <div>
                  <div className="mb-2 block text-sm font-medium text-gray-900 dark:text-white">
                    First name
                  </div>
                  <input
                    type="text"
                    name="First name"
                    id="First name"
                    value={firstName}
                    onChange={handleFirstNameChange}
                    className="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-gray-900 outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 sm:text-sm"
                    placeholder="Enter your first name"
                    required
                  />
                </div>
                <div>
                  <div className="mb-2 block text-sm font-medium text-gray-900 dark:text-white">
                    Last name
                  </div>
                  <input
                    type="text"
                    name="Last name"
                    id="Last name"
                    value={lastName}
                    onChange={handleLastNameChange}
                    className="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-gray-900 outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 sm:text-sm"
                    placeholder="Enter your last name"
                    required
                  />
                </div>
              </div>
              <div>
                <div className="mb-2 block text-sm font-medium text-gray-900 dark:text-white">
                  {Strings.EMAIL}
                </div>
                <input
                  type="email"
                  name="email"
                  id="email"
                  value={email}
                  onChange={handleEmailChange}
                  className="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-gray-900 outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 sm:text-sm"
                  placeholder="<EMAIL>"
                  required
                />
                {errors.email && (
                  <p className="mt-2 font-outfit text-xs font-light text-red-500">
                    {errors.email}
                  </p>
                )}
              </div>
              <div>
                <div className="mb-2 block text-sm font-medium text-gray-900 dark:text-white">
                  Your company name
                </div>
                <input
                  type="email"
                  name="companyName"
                  id="companyName"
                  value={companyName}
                  onChange={handleCompanyNameChange}
                  className="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-gray-900 outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 sm:text-sm"
                  placeholder="Enter your company name"
                  required
                />
              </div>
              <div className="relative">
                <div className="mb-2 block text-sm font-medium text-gray-900 dark:text-white">
                  {Strings.PASSWORD}
                </div>
                <input
                  type={showpassword ? "text" : "password"}
                  value={password}
                  onChange={handlePasswordChange}
                  name="password"
                  id="password"
                  placeholder="Password"
                  required
                  className="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-gray-900 outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 sm:text-sm"
                />
                {passwordError && (
                  <p className="mt-2 font-outfit text-xs font-light text-red-500">
                    {passwordError}
                  </p>
                )}
                <span
                  className="absolute right-3 top-[50px] flex -translate-y-1/2 transform cursor-pointer items-center text-lg"
                  onClick={togglepasswordVisibility}
                >
                  {showpassword ? <EyeInvisibleOutlined /> : <EyeOutlined />}
                </span>
              </div>
              <div className="relative">
                <div className="mb-2 block text-sm font-medium text-gray-900 dark:text-white">
                  {Strings.CONFIRM_NEW_PASSWORD}
                </div>
                <input
                  type={showpassword1 ? "text" : "password"}
                  value={confirmpassword}
                  onChange={handleConfirmPasswordChange}
                  name="password"
                  id="password"
                  placeholder="Re-enter your password"
                  required
                  className="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-gray-900 outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 sm:text-sm"
                />
                {confirmPasswordError && (
                  <p className="mt-2 font-outfit text-xs font-light text-red-500">
                    {confirmPasswordError}
                  </p>
                )}
                <span
                  className="absolute right-3 top-[50px] flex -translate-y-1/2 transform cursor-pointer items-center text-lg"
                  onClick={togglepasswordVisibility1}
                >
                  {showpassword1 ? <EyeInvisibleOutlined /> : <EyeOutlined />}
                </span>
              </div>
              <div className="flex items-center justify-start">
                <input
                  type="checkbox"
                  className="form-checkbox h-2 w-2 cursor-pointer bg-black"
                  checked={rememberMe}
                  onChange={() => {
                    setRememberMe(!rememberMe);
                  }}
                />
                <h1 className="flex items-center">
                  I accept{" "}
                  <Link
                    href="/"
                    className="bg-gradient-to-l from-[#bc7666] to-[#8D3F42] bg-clip-text text-sm font-medium text-transparent hover:underline"
                  >
                    &nbsp;terms & conditions
                  </Link>
                </h1>
              </div>

              <button
                onClick={handleSubmit}
                type="submit"
                className={`from-tosca to-contessa hover:from-contessa hover:to-tosca w-full rounded-full bg-black bg-gradient-to-l py-3 text-center font-outfit text-lg font-medium text-white hover:bg-gradient-to-l ${
                  loading ? "cursor-not-allowed opacity-50" : ""
                }`}
                disabled={loading}
              >
                Continue
              </button>

              <div className="flex justify-center">
                <p className="font-outfit text-sm font-light text-gray-500 dark:text-gray-400">
                  {Strings.if_you_dont_have}
                </p>
              </div>
            </div>
          </div>
          <div className="flex justify-center py-4 text-black">
            <p>{Strings.Are_you_a_dev} </p>
            <Link href={process.env.NEXT_PUBLIC_DEVELOPER_URL || "#"}>
              <button className="text-contessa ml-[5px] cursor-pointer underline">
                {Strings.Login_here}
              </button>
            </Link>
          </div>
        </div>
      </section>
    </>
  );
};

Register.getLayout = (page: any) => {
  return <BlankLayout forceLightMode>{page}</BlankLayout>;
};

export async function getServerSideProps(context: any) {
  const token = context.req.cookies.token;

  if (token) {
    return {
      redirect: {
        destination: "/",
        permanent: false,
      },
    };
  }

  return { props: {} };
}

export default Register;

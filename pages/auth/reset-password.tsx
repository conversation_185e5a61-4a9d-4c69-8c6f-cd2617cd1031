import { EyeInvisibleOutlined, EyeOutlined } from "@ant-design/icons";
import { Images, Strings } from "@/constants";
import React, { useRef, useState } from "react";

import BlankLayout from "@/components/Layouts/BlankLayout";
import { IRootState } from "@/store";
import Image from "next/image";
import { LabelComponent } from "@/components/label";
import axios from "axios";
import { toast } from "react-toastify";
import { useRouter } from "next/router";
import { useSelector } from "react-redux";

const Resetpassword = () => {
  const router = useRouter();
  const [passwordError, setPasswordError] = useState<string | null>(null);
  const [confirmPasswordError, setConfirmPasswordError] = useState<
    string | null
  >(null);
  const [showpassword, setShowpassword] = useState(false);
  const [showpassword1, setShowpassword1] = useState(false);
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [confirmpassword, setConfirmPassword] = useState("");
  const [error, setError] = useState("");
  const [otp, setOtp] = useState<string[]>(Array(6).fill(""));
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);
  const emailId = useSelector((state: IRootState) => state.email.emailId);
  const handleChange = (value: string, index: number) => {
    if (/[^0-9]/.test(value)) {
      return;
    }

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyDown = (
    e: React.KeyboardEvent<HTMLInputElement>,
    index: number
  ) => {
    if (e.key === "Backspace" && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
    const pastedData = e.clipboardData.getData("text").slice(0, 6).split("");
    setOtp((prevOtp) =>
      pastedData.map((char, i) => (/\d/.test(char) ? char : prevOtp[i]))
    );

    pastedData.forEach((char, i) => {
      if (inputRefs.current[i] && /\d/.test(char)) {
        inputRefs.current[i]!.value = char;
      }
    });
  };
  const togglepasswordVisibility = () => {
    setShowpassword(!showpassword);
  };
  const togglepasswordVisibility1 = () => {
    setShowpassword1(!showpassword1);
  };
  const handleSubmit = async () => {
    if (password !== confirmpassword) {
      setError("Passwords do not match.");
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      await axios.post(
        `${process.env.NEXT_PUBLIC_API_URL}user/forgotPassword`,
        {
          emailId: emailId,
          OTP: otp,
          password: password,
          confirmPassword: password,
        },
        {
          headers: {
            accept: "*/*",
            "Content-Type": "application/json",
          },
        }
      );
      toast.success("Password reset successfully");
      router.push("/auth/login", undefined, { shallow: true });
    } catch (error) {
      setLoading(false);
      toast.error("Invalid OTP or OTP has been expired");
    }
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPassword(e.target.value);
    if (e.target.value.length < 6) {
      setPasswordError("Password must be at least 6 characters long.");
    } else {
      setPasswordError(null);
    }
  };

  const handleConfirmPasswordChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setConfirmPassword(e.target.value);
    if (e.target.value !== password) {
      setConfirmPasswordError("Please confirm your password.");
    } else {
      setConfirmPasswordError(null);
    }
  };

  return (
    <div className="mx-auto flex w-full flex-col items-center justify-center px-6 py-8 xs:h-[90vh] md:h-screen lg:py-0">
      <button className="mb-6 flex items-center text-2xl font-semibold">
      </button>
      <div className="w-full rounded-lg bg-white shadow-[inset_0_0_4px_4px_rgba(0,0,0,0.1)] dark:border dark:border-gray-700 dark:bg-gray-800 sm:max-w-md md:mt-0 xl:p-0 ">
        <div className="space-y-4 p-6 sm:p-8 md:space-y-6  ">
          <LabelComponent
            label={Strings.RESET_PASSWORD}
            className="font-outfit text-xl font-bold md:text-2xl"
          />
          <div>
            <LabelComponent
              label={Strings.OTP}
              className="mb-2 block text-sm font-medium text-gray-900 dark:text-white"
            />
            <div className="flex items-center justify-between">
              {otp.map((digit, index) => (
                <input
                  key={index}
                  type="text"
                  value={digit}
                  onChange={(e) =>
                    handleChange(e.target.value.slice(-1), index)
                  }
                  onKeyDown={(e) => handleKeyDown(e, index)}
                  onPaste={handlePaste}
                  ref={(el) => (inputRefs.current[index] = el)}
                  className="h-8 w-8 rounded-md border border-gray-300 text-center text-base font-semibold outline-none md:h-12 md:w-12 "
                  maxLength={1}
                />
              ))}
            </div>
          </div>
          <div className="relative">
            <LabelComponent
              label={Strings.NEW_PASSWORD}
              className="mb-2 block text-sm font-medium text-gray-900 dark:text-white"
            />
            <input
              type={showpassword ? "text" : "password"}
              value={password}
              onChange={handlePasswordChange}
              name="password"
              id="password"
              placeholder="New Password"
              required
              className="focus:ring-primary-600 focus:border-primary-600 block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-gray-900 outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500 sm:text-sm"
            />
            {passwordError && (
              <p className="mt-2 font-outfit text-xs font-light text-red-500">
                {passwordError}
              </p>
            )}
            <span
              className="absolute right-3 top-[50px] flex -translate-y-1/2 transform cursor-pointer items-center text-lg"
              onClick={togglepasswordVisibility}
            >
              {showpassword ? <EyeInvisibleOutlined /> : <EyeOutlined />}
            </span>
          </div>
          <div className="relative">
            <LabelComponent
              label={Strings.CONFIRM_NEW_PASSWORD}
              className="mb-2 block text-sm font-medium text-gray-900 dark:text-white"
            />
            <input
              type={showpassword1 ? "text" : "password"}
              value={confirmpassword}
              onChange={handleConfirmPasswordChange}
              name="password"
              id="password"
              placeholder="Confirm New Password"
              required
              className="focus:ring-primary-600 focus:border-primary-600 block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-gray-900 outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500 sm:text-sm"
            />
            {confirmPasswordError && (
              <p className="mt-2 font-outfit text-xs font-light text-red-500">
                {confirmPasswordError}
              </p>
            )}

            <span
              className="absolute right-3 top-[50px] flex -translate-y-1/2 transform cursor-pointer items-center text-lg"
              onClick={togglepasswordVisibility1}
            >
              {showpassword1 ? <EyeInvisibleOutlined /> : <EyeOutlined />}
            </span>
          </div>
          {error && (
            <p className="mt-2 font-outfit text-sm font-light text-red-500">
              {error}
            </p>
          )}

          <button
            type="submit"
            onClick={handleSubmit}
            className={`from-tosca to-contessa w-full rounded-full bg-black bg-gradient-to-l py-3 text-center font-outfit text-lg font-medium text-white ${
              loading ? "cursor-not-allowed opacity-50" : ""
            }`}
          >
            {loading ? "Loading..." : Strings.SUBMIT}
          </button>
        </div>
      </div>
    </div>
  );
};
Resetpassword.getLayout = (page: any) => {
  return <BlankLayout forceLightMode>{page}</BlankLayout>;
};
export default Resetpassword;

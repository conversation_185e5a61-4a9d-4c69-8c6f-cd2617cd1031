import "react-datepicker/dist/react-datepicker.css";

import {
  CalendarOutlined,
  CloseOutlined,
  DownOutlined,
  ExclamationCircleOutlined,
  LinkOutlined,
  MailOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import Customizecontent, {
  toggleModal,
} from "@/components/Modals/Customizecontent";
import { Fragment, useEffect, useRef, useState } from "react";
import { Images, Strings } from "@/constants";
import InviteCandidate, {
  toggleInviteCandidateModal,
} from "@/components/Modals/InviteCandidate";
import Managetests, { toggleModal1 } from "@/components/Modals/Managetests";
import Quickdamo, { toggleModal2 } from "@/components/Modals/Quickdamo";
import ReportModal, {
  toggleReportModal,
} from "@/components/Modals/ReportModal";
import UpgradePlaneModal, {
  toggleUpgradeModal,
} from "@/components/Modals/UpgradePlaneModal";

import { Checkbox } from "antd";
import type { CheckboxProps } from "antd";
import Cookies from "js-cookie";
import DatePicker from "react-datepicker";
import Image from "next/image";
import Loader from "@/components/Layouts/Loader";
import Pagination from "@/components/pagination";
import React from "react";
import { Tab } from "@headlessui/react";
import { Tooltip } from "antd";
import { ISubscription, IUser } from "@/interface";
import axios from "axios";
import { useRouter } from "next/router";

interface TableRowData {
  id: string;
  userId: string | null;
  name: string | null;
  testTaken: string | null;
  dateTaken: string | null;
  techStack: string[];
  softSkills: string | null;
  question: string[];
  answer: string[];
  video: string | null;
  success: boolean;
  skillRating: string | null;
  feedback: string | null;
  task: string | null;
  code: string | null;
  codeFeedback: string | null;
  email: string | null;
  createdAt: string;
  updatedAt: string;
  proctoringResult: string | null;
}
const gptvetting: React.FC = () => {
  const router = useRouter()
  const [user, _] = useState(() => {
    if (typeof window === "undefined") return null;
    const userJson = localStorage.getItem("userData");
    return JSON.parse(userJson!) as IUser;
  });
  const [isOpen, setIsOpen] = useState(false);
  const [upgradeplan, setUpgradeplan] = useState(false);
  const [reportModal, setReportModal] = useState(false);
  const [open, setOpen] = useState(false);
  const [customizecontent, setCustomizecontent] = useState(false);
  const [quickdemo, setQuickdemo] = useState(false);
  const [managesavedtests, setManagesavedtests] = useState(false);
  const [filter, setFilter] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [searchTerm1, setSearchTerm1] = useState("");
  const [loading, setLoading] = useState(false);
  const [isChecked, setChecked] = useState(false);
  const [archivedData, setArchivedData] = useState<any[]>([]);
  const [popoverIndex, setPopoverIndex] = useState<number | null>(null);
  const [connectedRows, setConnectedRows] = useState<number[]>([]);
  const [TableData, setTableData] = useState<TableRowData[]>([]);
  const [currentPage, setCurrentPage] = useState(0);
  const perPage = 5;
  const [svgVisibleArray, setSvgVisibleArray] = useState<boolean[]>([]);
  const [popoverVisibleArray, setPopoverVisibleArray] = useState<boolean[]>([]);
  const pageCount = Math.ceil(TableData.length / perPage);
  const startIndex = currentPage * perPage;
  const endIndex = startIndex + perPage;
  const currentData = TableData.slice(startIndex, endIndex);
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [isOpen1, setIsOpen1] = useState(false);
  const popoverRefs = useRef<Array<HTMLDivElement | null>>([]);
  const totalRecords = TableData.length;

  useEffect(() => {
    const fetchTableData = async () => {
      try {
        const response = await axios.get(
          `${process.env.NEXT_PUBLIC_API_URL}gptVetting/get`
        );
        const apiData = response.data.gptVettingData || []; // Ensure it's an array

        const mappedData: TableRowData[] = apiData.map((item: any) => ({
          id: item.id,
          name: item.name || "N/A",
          testTaken: item.testTaken || "N/A",
          dateTaken: item.dateTaken || "N/A",
          techStack: item.techStack || [],
          softSkills: item.softSkills || "N/A",
          question: item.question || [],
          answer: item.answer || [],
          video: item.video || null,
          success: item.success || false,
          skillRating: item.skillRating || null,
          feedback: item.feedback || null,
          task: item.task || null,
          code: item.code || null,
          codeFeedback: item.codeFeedback || null,
          email: item.email || null,
          createdAt: item.createdAt || "",
          updatedAt: item.updatedAt || "",
          proctoringResult: item.proctoringResult || null,
        }));

        setTableData(mappedData);
      } catch (error) {
        console.error("Error fetching table data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchTableData();
  }, []);

  const handleChangePage = (selectedPage: number) => {
    setCurrentPage(selectedPage);
  };

  const filteredConnectedRows = connectedRows.filter((connectedIndex) => {
    const connectedRow = TableData[connectedIndex];
    return (
      connectedRow &&
      connectedRow.name?.toLowerCase().includes(searchTerm1.toLowerCase())
    );
  });

  const handleRemoveAndArchive = () => {
    if (popoverIndex !== null) {
      const removedRow = TableData[popoverIndex];

      const newSvgVisibleArray = [...svgVisibleArray];
      newSvgVisibleArray[popoverIndex] = false;
      setSvgVisibleArray(newSvgVisibleArray);

      setConnectedRows((prevConnectedRows) =>
        prevConnectedRows.filter((i) => i !== popoverIndex)
      );

      setTableData((prevTableData) =>
        prevTableData.filter((_, i) => i !== popoverIndex)
      );
      setArchivedData([...archivedData, removedRow]);

      setPopoverIndex(null);
      setOpen(false);
    }
  };

  const onChange: CheckboxProps["onChange"] = (e) => {
    setChecked(e.target.checked);
  };
  const onIconClick = () => {
    setChecked(false);
  };

  const handleMarkAsContected = (index: number) => {
    const newSvgVisibleArray = [...svgVisibleArray];
    newSvgVisibleArray[index] = true;
    setSvgVisibleArray(newSvgVisibleArray);

    setConnectedRows((prevConnectedRows) => {
      const newConnectedRows = [...prevConnectedRows, index];
      console.log("Connected Rows:", newConnectedRows);
      return newConnectedRows;
    });
  };

  const archiveRow = (connectedRow: TableRowData, index: number) => {
    setArchivedData((prevArchivedData) => [...prevArchivedData, connectedRow]);

    setTableData((prevTableData) =>
      prevTableData.filter((_, i) => i !== index)
    );

    setConnectedRows((prevConnectedRows) =>
      prevConnectedRows.filter((i) => i !== index)
    );

    setSvgVisibleArray((prevSvgVisibleArray) => {
      const newSvgVisibleArray = [...prevSvgVisibleArray];
      newSvgVisibleArray[index] = false;
      return newSvgVisibleArray;
    });
  };

  const handleUndo = (rowData: TableRowData, index: number) => {
    setArchivedData((prevArchivedData) => {
      console.log("Previous Archived Data:", prevArchivedData);
      const updatedArchivedData = prevArchivedData.filter(
        (_, i) => i !== index
      );
      console.log("Updated Archived Data:", updatedArchivedData);
      return updatedArchivedData;
    });

    setTableData((prevTableData) => {
      console.log("Previous Table Data:", prevTableData);
      const updatedTableData = [...prevTableData, rowData];
      console.log("Updated Table Data:", updatedTableData);
      return updatedTableData;
    });
  };

  const tooltipContent2 = (
    <span className="text-xs ">
      Customize the welcome screen and candidate invitation email easily.
    </span>
  );

  const tooltipContent4 = (
    <span className="text-xs">
      Candidates who have defined their own skills.
    </span>
  );

  const disableBodyScroll = () => {
    document.body.style.overflow = "hidden";
  };

  const enableBodyScroll = () => {
    document.body.style.overflow = "";
  };

  useEffect(() => {
    return () => {
      enableBodyScroll();
    };
  }, []);

  const openModal2 = (index: number) => {
    setPopoverIndex(index);
    setOpen(true);
    disableBodyScroll();
  };

  const openModal7 = () => {
    setFilter(!filter);
    disableBodyScroll();
  };

  const handlePopoverToggle = () => {
    setIsOpen1(!isOpen1);
  };

  const handleTogglePopover = (index: number) => {
    setPopoverVisibleArray((prev) => {
      const updated = [...prev];
      updated[index] = !updated[index];
      return updated.map((item, i) => (i === index ? updated[i] : false));
    });
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        !popoverRefs.current.some((ref) => ref?.contains(event.target as Node))
      ) {
        setPopoverVisibleArray([]);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleButtonClick = () => {
    toggleModal(customizecontent, setCustomizecontent);
    disableBodyScroll();
  };
  const handleButtonClick1 = () => {
    toggleModal1(managesavedtests, setManagesavedtests);
    disableBodyScroll();
  };
  const handleButtonClick2 = () => {
    toggleModal2(quickdemo, setQuickdemo);
    disableBodyScroll();
  };
  const handleReportModal = () => {
    toggleReportModal(reportModal, setReportModal);
    disableBodyScroll();
  };
  const handleUpgradeButtonClick = () => {
    toggleUpgradeModal(upgradeplan, setUpgradeplan);
    disableBodyScroll();
  };
  const handleInviteCandidateClick = () => {
    toggleInviteCandidateModal(isOpen, setIsOpen);
    disableBodyScroll();
  };

  const openReport = (id: string) => {
    router.push(`/ai-interview-report/${id}`);
  }

  const handleRowClick = (id: string) => {
    try {
      console.log('Clicking row with ID:', id);
      router.push(`/ai-interview-report/${id}`);
    } catch (error) {
      console.error('Navigation error:', error);
    }
  };

  return loading ? (
    <div>
      <Loader />
    </div>
  ) : (
    <div>
      <Customizecontent
        customizecontent={customizecontent}
        setCustomizecontent={setCustomizecontent}
      />
      <Managetests
        managesavedtests={managesavedtests}
        setManagesavedtests={setManagesavedtests}
      />
      <Quickdamo quickdemo={quickdemo} setQuickdemo={setQuickdemo} />
      <InviteCandidate isOpen={isOpen} setIsOpen={setIsOpen} />
      <div className="flex items-center xs:flex-col md:flex-row md:justify-between ">
        <div className="flex items-center space-x-2 xs:mb-4 md:mb-0">
          <h1 className="md:text-xl- font-bold text-black dark:text-white xs:text-2xl xl:text-xl">
            {Strings.GPT_VETTING}
          </h1>
          <p className=" flex items-center justify-center rounded-full bg-[#8d3f42] bg-opacity-[20%] xs:h-6 xs:w-6 xl:h-8 xl:w-8 ">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="text-[#8d3f42] xs:h-4 xs:w-4 xl:h-6  xl:w-6"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </p>
        </div>
        <div className="flex items-center xs:flex-col sm:space-x-1 md:flex-row xl:space-x-2">
          <div className="flex">
            <button
              onClick={handleButtonClick}
              className="xs:text:xs mr-1 flex transform cursor-pointer items-center font-semibold text-[#8d3f42] transition-transform hover:scale-105 dark:text-white xl:text-sm"
            >
              {Strings.Customize_content}
            </button>
            <Tooltip title={tooltipContent2} placement="bottom">
              <ExclamationCircleOutlined rev={undefined} />
            </Tooltip>
          </div>
          <p className="h-4 border-[1.5px] border-l border-[#8d3f42]" />
          <button
            onClick={handleButtonClick1}
            className="xs:text:xs transform cursor-pointer font-semibold text-[#8d3f42] transition-transform hover:scale-105 dark:text-white xl:text-sm"
          >
            {Strings.Manage_saved_tests}
          </button>

          <p className="h-4 border-[1.5px] border-l border-[#8d3f42]" />
          <button
            onClick={handleButtonClick2}
            className="nav-item group flex h-10 transform items-center justify-center rounded-full bg-[#8d3f42] shadow-lg transition-transform
         hover:scale-105 dark:bg-white xs:my-2 xs:w-[300px] sm:w-full md:my-0 md:w-28"
          >
            <Image
              src={Images.quickdamo}
              alt="/"
              height={16}
              width={16}
              className="mr-1 brightness-0 invert filter dark:filter-none"
            />
            <text className="text-sm font-semibold text-white dark:text-black">
              {Strings.Quick_demo}
            </text>
          </button>
          {user?.subscription?.status === "paid" ? (
            <button
              onClick={handleInviteCandidateClick}
              className="flex h-10 transform items-center justify-center rounded-full border border-[#8D3F42] bg-white px-2 text-sm font-semibold text-[#8D3F42] transition-transform hover:scale-105 hover:bg-[#8D3F42] hover:text-white dark:border-transparent dark:bg-[#8D3F42]  dark:text-white dark:hover:bg-white dark:hover:text-[#8D3F42]"
            >
              <MailOutlined rev={undefined} />
              <text className="ml-1">{Strings.Inviteacandidate}</text>
            </button>
          ) : (
            <button
              onClick={handleUpgradeButtonClick}
              className="flex h-12 transform items-center justify-center rounded-full bg-[#8D3F42] bg-opacity-[10%] text-sm font-bold text-black transition-transform hover:scale-105 dark:bg-[#8D3F42] dark:bg-opacity-[10%] dark:text-white xs:mt-2 xs:w-full md:mt-0 md:w-32"
            >
              <Image
                src={Images.Upgradeplane}
                alt="/"
                height={16}
                width={16}
                className={`mr-1`}
              />
              Subscribe Plan
            </button>
          )}
        </div>
      </div>
      {/* <div className="flex xs:justify-center md:justify-end">
    <button
      onClick={() => {
        window.open("/technical-vetting", "_blank");
      }}
      className="mx-2 mt-1 flex transform cursor-pointer items-center justify-end text-[#8d3f42] transition-transform hover:scale-105"
    >
      <LinkOutlined rev={undefined} className="mr-1 rotate-45" />
      Copy Link Instead
    </button>
  </div> */}
      <Tab.Group>
        <Tab.List className="mt-3 flex flex-wrap border-b border-white-light dark:border-[#8D3F42] xs:text-[12px] xs:font-normal md:text-sm md:font-semibold">
          <Tab as={Fragment}>
            {({ selected }) => (
              <button
                className={`${
                  selected
                    ? "rounded-t-md !border-white-light !border-b-white text-black !outline-none dark:!border-[#8D3F42] dark:!border-b-black dark:text-white "
                    : ""
                }
                -mb-[1px] block border border-transparent dark:hover:border-b-black dark:hover:text-white xs:p-2 xs:py-1 md:p-4 md:py-3`}
              >
                {Strings.Reports}({totalRecords})
              </button>
            )}
          </Tab>
          <Tab as={Fragment}>
            {({ selected }) => (
              <button
                className={`${
                  selected
                    ? "rounded-t-md !border-white-light !border-b-white text-black !outline-none dark:!border-[#8D3F42] dark:!border-b-black dark:text-white"
                    : ""
                }
                -mb-[1px] block border border-transparent dark:hover:border-b-black dark:hover:text-white xs:p-2 xs:py-1 md:p-4 md:py-3`}
              >
                {Strings.Contected}({connectedRows.length})
              </button>
            )}
          </Tab>
          <Tab as={Fragment}>
            {({ selected }) => (
              <button
                className={`${
                  selected
                    ? "rounded-t-md !border-white-light !border-b-white text-black !outline-none dark:!border-[#8D3F42] dark:!border-b-black dark:text-white"
                    : ""
                }
                -mb-[1px] block border border-transparent dark:hover:border-b-black dark:hover:text-white xs:p-2 xs:py-1 md:p-4 md:py-3`}
              >
                {Strings.Archived}({archivedData.length})
              </button>
            )}
          </Tab>
        </Tab.List>
        <Tab.Panels>
          {/* < Hired tab section > */}
          <Tab.Panel>
            <div className="mt-4 w-full rounded-xl bg-white p-4 shadow-[inset_0_0_4px_4px_rgba(0,0,0,0.1)] dark:bg-black-dark-light">
              <div className="flex xs:flex-col md:flex-row md:justify-between">
                <div className="flex xs:justify-between md:space-x-2">
                  <div className="relative">
                    <input
                      type="text"
                      placeholder="Search"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="flex h-12 items-center rounded-lg border-[1.7px] border-[#dbdbdb] bg-white px-3 pl-8 text-sm text-black outline-none dark:border-dark-Cod_Gray dark:bg-gray-300 xs:w-[158px] md:w-[180px]"
                    />
                    <span className="item-center absolute left-3 top-1/2 flex -translate-y-1/2 transform text-black">
                      <SearchOutlined />
                    </span>
                  </div>
                  <button
                    onClick={openModal7}
                    className="flex h-12 w-[85px] items-center justify-center rounded-lg border-[1.7px] border-[#dbdbdb] bg-white text-sm text-black dark:border-dark-Cod_Gray dark:bg-gray-300 "
                  >
                    <Image
                      src={Images.Filter}
                      alt="/"
                      height={500}
                      width={500}
                      className="mr-1 h-4 w-4"
                    />
                    {Strings.Filters}
                  </button>
                </div>
                {filter && (
                  <div className="fixed left-0 top-0 z-50 flex h-full w-full items-center justify-center bg-gray-500 bg-opacity-[40%] backdrop-blur-sm ">
                    <div className="items-center- relative mx-5 flex min-h-[250px] max-w-full justify-center rounded-lg bg-white p-4 xs:w-full md:mx-0 md:w-[480px] ">
                      <div className="absolute right-2 top-2">
                        <button
                          title="Close"
                          className="flex h-8 w-8 items-center justify-center rounded-full bg-white text-black opacity-[80%]"
                          onClick={openModal7}
                        >
                          <CloseOutlined rev={undefined} />
                        </button>
                      </div>
                      <div className="space-y-4">
                        <h1 className="text-xl font-bold text-black">
                          {Strings.Filters}
                        </h1>
                        <p className="text-black">{Strings.Filter_by_test}</p>
                        <div className="w-full">
                          <h1
                            className={`flex w-full cursor-pointer justify-between p-4 text-base font-medium ${
                              isOpen1
                                ? "rounded-t-md border-x border-t"
                                : "rounded-md border"
                            }`}
                            onClick={handlePopoverToggle}
                          >
                            <p className="text-black">Select</p>
                            <DownOutlined
                              className={`transform transition-transform ${
                                isOpen1 ? "rotate-180" : "rotate-0"
                              }`}
                            />
                          </h1>
                          <div
                            className={`w-full overflow-hidden rounded-b-md transition-all duration-300 ease-in-out ${
                              isOpen1 ? "max-h-40 border shadow-md" : "max-h-0"
                            }`}
                          >
                            <div className="max-h-full w-full space-y-3 bg-white p-4">
                              <div className="relative">
                                <input
                                  type="text"
                                  placeholder="Search"
                                  className="flex w-full items-center rounded-lg border-[1.7px] bg-white p-3 pl-10 text-black outline-none"
                                />
                                <span className="item-center absolute left-3 top-1/2 flex -translate-y-1/2 transform text-black">
                                  <SearchOutlined />
                                </span>
                              </div>
                              <div
                                className={` inline-block rounded-full bg-gray-200 px-2
                         ${isChecked ? "visible" : "hidden"}`}
                              >
                                {Strings.Candidate_self_defined_skills}
                                <CloseOutlined
                                  rev={undefined}
                                  className="ml-1 cursor-pointer "
                                  onClick={onIconClick}
                                />
                              </div>
                              <div className="flex items-center rounded-md bg-gray-200 p-2 text-sm font-light text-black">
                                <Checkbox
                                  onChange={onChange}
                                  checked={isChecked}
                                >
                                  {Strings.Candidate_self_defined_skills}
                                  <Tooltip
                                    title={tooltipContent4}
                                    placement="top"
                                  >
                                    <ExclamationCircleOutlined
                                      rev={undefined}
                                      className="ml-1"
                                    />
                                  </Tooltip>
                                </Checkbox>
                              </div>
                            </div>
                          </div>
                        </div>
                        <p className="text-black">{Strings.Date_taken}</p>
                        <div className="flex items-center xs:flex-col md:flex-row">
                          <div className="relative flex items-center">
                            <DatePicker
                              selected={startDate}
                              onChange={(date: Date | null) =>
                                setStartDate(date)
                              }
                              selectsStart
                              startDate={startDate}
                              endDate={endDate}
                              dateFormat="dd/MM/yyyy"
                              placeholderText="Start Date"
                              className=" block w-full rounded-md border border-[#dbdbdb] bg-gray-50 p-3 text-base text-gray-900 outline-none "
                            />
                            <CalendarOutlined className="absolute xs:right-2 md:right-7" />
                          </div>
                          <span className="text-gray-500 xs:my-2 md:mx-4">
                            {Strings.to}
                          </span>
                          <div className="relative flex items-center">
                            <DatePicker
                              selected={endDate}
                              onChange={(date: Date | null) => setEndDate(date)}
                              selectsEnd
                              startDate={startDate}
                              endDate={endDate}
                              minDate={startDate}
                              dateFormat="dd/MM/yyyy"
                              placeholderText="End Date"
                              className=" block w-full rounded-md border border-[#dbdbdb] bg-gray-50 p-3 text-base text-gray-900 outline-none "
                            />
                            <CalendarOutlined className="absolute xs:right-2 md:right-7" />
                          </div>
                        </div>
                        <div className="flex justify-end">
                          <button className="flex w-28 transform items-center justify-center rounded-full border bg-[#8D3F42] p-4 text-base font-semibold text-white transition-transform hover:scale-105">
                            {Strings.Apply}
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
                <div>
                  {user?.subscription?.status === "paid" && (
                    <button
                      onClick={handleUpgradeButtonClick}
                      className="flex h-12 transform items-center justify-center rounded-full bg-[#8D3F42] bg-opacity-[10%] text-sm font-bold text-black transition-transform hover:scale-105 dark:bg-[#8D3F42] dark:bg-opacity-[10%] dark:text-white xs:mt-2 xs:w-full md:mt-0 md:w-32"
                    >
                      <Image
                        src={Images.Upgradeplane}
                        alt="/"
                        height={16}
                        width={16}
                        className={`mr-1`}
                      />

                      {Strings.Upgrade_plan}
                    </button>
                  )}
                  <UpgradePlaneModal
                    upgradeplan={upgradeplan}
                    setUpgradeplan={setUpgradeplan}
                  />
                </div>
              </div>
              <div className="relative- min-h-auto no-scrollbar overflow-y-scroll- mt-3 overflow-x-auto shadow-sm sm:rounded-lg">
                <table className="w-full text-left text-sm text-gray-500 dark:text-gray-400 rtl:text-right">
                  <thead
                    className={`sticky top-0 z-20 bg-[#d7bfbf] text-sm font-light text-black  dark:text-white`}
                  >
                    <tr className="">
                      <th scope="col" className="px-6 py-2 dark:bg-[#863e41]">
                        {Strings.Name}
                      </th>
                      <th scope="col" className="px-6 py-2 dark:bg-[#863e41]">
                        {Strings.Test}
                      </th>
                      <th scope="col" className="px-6 py-2 dark:bg-[#863e41]">
                        {Strings.Date_taken}
                      </th>
                      <th scope="col" className="px-6 py-2 dark:bg-[#863e41]">
                        {Strings.Main_tech_stacks}
                      </th>
                      <th scope="col" className="px-6 py-2 dark:bg-[#863e41]">
                      {Strings.AI_Ratings}                  
                      </th>
                      <th scope="col" className="px-6 py-2 dark:bg-[#863e41]">
                        {Strings.Proctoring_result}
                      </th>
                      <th scope="col" className=" px-6 py-2 dark:bg-[#863e41]">
                        <span className="sr-only">{Strings.Edit}</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {currentData.length === 0 ? (
                      <tr className="text-center">
                        <td colSpan={5} className="py-8">
                          <div className="flex flex-col items-center justify-center">
                            <Image
                              src={Images.NoRecords}
                              alt="/"
                              height={90}
                              width={90}
                            />
                            <span className="text-base font-bold text-black dark:text-white">
                              {Strings.No_records_found}
                            </span>
                          </div>
                        </td>
                      </tr>
                    ) : (
                      currentData
                        .filter((rowData) =>
                          (rowData.name || "")
                            .toLowerCase()
                            .includes((searchTerm || "").toLowerCase())
                        )
                        .map((rowData, index) => (
                          <tr
                            key={rowData.id}
                            onClick={(e) => {
                              e.preventDefault();
                              if (rowData.id) {
                                handleRowClick(rowData.id);
                              }
                            }}
                            className="relative border-b bg-white hover:bg-gray-100 cursor-pointer dark:border-gray-700 dark:bg-[#1B1B1B] dark:hover:bg-dark-tundora"
                          >
                            <th
                              scope="row"
                              className="flex items-center whitespace-nowrap px-6 py-3 font-medium text-gray-900 dark:text-white"
                            >
                              {rowData.name || "N/A"}
                              {svgVisibleArray[
                                currentPage * perPage + index
                              ] && (
                                <Image
                                  src={Images.Contacted}
                                  alt="/"
                                  height={14}
                                  width={14}
                                  className="ml-2"
                                />
                              )}
                            </th>
                            <td className="px-6 py-3">
                              {rowData.testTaken || "N/A"}
                            </td>
                            <td className="px-6 py-3">
                              {rowData.createdAt
                                ? new Date(
                                    rowData.createdAt
                                  ).toLocaleDateString("en-GB", {
                                    day: "2-digit",
                                    month: "2-digit",
                                    year: "numeric",
                                  })
                                : "N/A"}
                            </td>
                            <td className="px-6 py-3">
                              {rowData.techStack.length > 0
                                ? rowData.techStack.join(", ")
                                : "N/A"}
                            </td>
                            <td className="px-6 py-3">
                              {rowData.softSkills || "N/A"}
                            </td>
                            <td className="px-6 py-3">
                              {rowData.proctoringResult || "N/A"}
                            </td>
                            <td className="flex items-center justify-center whitespace-nowrap py-4 pr-16">
                              <div
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleTogglePopover(index);
                                }}
                                className="flex h-7 w-7 items-center justify-center rounded-lg border-[1.5px] border-[#dbdbdb] dark:bg-white"
                              >
                                <Image
                                  src={Images.dots}
                                  width={500}
                                  height={500}
                                  alt={"/"}
                                  className="h-5 w-5 cursor-pointer"
                                />
                              </div>
                            </td>
                            {popoverVisibleArray[index] && (
                              <div
                                ref={(el) => (popoverRefs.current[index] = el!)}
                                id="popover"
                                className="absolute right-2 top-11 z-10 rounded-lg border bg-white p-2 shadow-md"
                              >
                                <h1 className="space-y-4 text-base font-semibold">
                                  <p
                                    onClick={(e) => {
                                      e.preventDefault();
                                      handleMarkAsContected(
                                        currentPage * perPage + index
                                      );
                                    }}
                                    className="cursor-pointer text-black"
                                  >
                                    {Strings.Mark_as_contected}
                                  </p>
                                  <p
                                    onClick={(e) => {
                                      e.preventDefault();
                                      openReport(rowData.id);
                                    }}
                                    className="cursor-pointer text-black"
                                  >
                                    Report
                                  </p>
                                  <p
                                    onClick={(e) => {
                                      e.preventDefault();
                                      openModal2(index);
                                    }}
                                    className="cursor-pointer text-red-500"
                                  >
                                    {Strings.Archive}
                                  </p>
                                </h1>
                              </div>
                            )}
                          </tr>
                        ))
                    )}
                    {open && (
                      <div className="fixed left-0 top-0 z-50 flex h-full w-full items-center justify-center bg-gray-500 bg-opacity-[20%] backdrop-blur-sm">
                        <div className="flex w-[400px] items-center justify-center rounded-xl bg-white p-4">
                          <div className="text-center">
                            <div className="flex justify-center">
                              <div className="flex h-20 w-20 items-center justify-center rounded-full bg-yellow-300 bg-opacity-[20%] text-4xl text-yellow-600">
                                <ExclamationCircleOutlined rev={undefined} />
                              </div>
                            </div>
                            <h1 className="mx-10 my-3 text-lg font-semibold text-black">
                              {Strings.Are_You_sure}
                            </h1>
                            <div className="flex justify-center space-x-4">
                              <button
                                className="flex w-24 items-center justify-center rounded-full border border-[#8D3F42] bg-white p-4 text-base font-semibold text-[#8D3F42] hover:bg-[#8D3F42] hover:text-white "
                                onClick={() => setOpen(false)}
                              >
                                {Strings.Cancel}
                              </button>

                              <button
                                onClick={handleRemoveAndArchive}
                                className="flex w-28 transform items-center justify-center rounded-full border bg-[#8D3F42] p-4 text-base font-semibold text-white transition-transform hover:scale-105"
                              >
                                {Strings.Yes_remove}
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </tbody>
                </table>
                <Pagination
                  currentPage={currentPage}
                  handleChangePage={handleChangePage}
                  pageCount={pageCount}
                />
              </div>
            </div>
          </Tab.Panel>
          <Tab.Panel>
            <div className="mt-4 w-full rounded-xl bg-white p-4 shadow-[inset_0_0_4px_4px_rgba(0,0,0,0.1)] dark:bg-black-dark-light">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search"
                  value={searchTerm1}
                  onChange={(e) => setSearchTerm1(e.target.value)}
                  className="flex items-center rounded-lg border-[1.7px] border-[#dbdbdb] bg-white p-3 pl-10 text-black outline-none dark:border-dark-Cod_Gray dark:bg-gray-300 xs:w-[200px] md:w-[380px]"
                />
                <span className="item-center absolute left-3 top-1/2 flex -translate-y-1/2 transform text-black">
                  <SearchOutlined />
                </span>
              </div>
              <div className="relative- min-h-auto no-scrollbar mt-3 overflow-x-auto overflow-y-scroll shadow-sm sm:rounded-lg">
                <table className="w-full  text-left text-sm text-gray-500 dark:text-gray-400 rtl:text-right">
                  <thead className="bg-opacity-[20%]- sticky top-0 bg-[#d7bfbf] text-sm text-gray-700 dark:bg-dark-CodGray dark:text-white">
                    <tr>
                      <th scope="col" className="px-6 py-2 dark:bg-[#863e41]">
                        {Strings.Name}
                      </th>
                      <th scope="col" className="px-6 py-2 dark:bg-[#863e41]">
                        {Strings.Test}
                      </th>
                      <th scope="col" className="px-6 py-2 dark:bg-[#863e41]">
                        {Strings.Date_taken}
                      </th>
                      <th scope="col" className="px-6 py-2 dark:bg-[#863e41]">
                        {Strings.Main_tech_stacks}
                      </th>
                      <th scope="col" className="px-6 py-2 dark:bg-[#863e41]">
                      {Strings.AI_Ratings}
                      </th>
                      <th scope="col" className="px-6 py-2 dark:bg-[#863e41]">
                        {Strings.Proctoring_result}
                      </th>
                      <th scope="col" className="px-6 py-2 dark:bg-[#863e41]">
                        <span className="sr-only">{Strings.Edit}</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredConnectedRows.length === 0 ? (
                      <tr className="text-center">
                        <td colSpan={7} className="py-8">
                          <div className="flex flex-col items-center justify-center">
                            <Image
                              src={Images.NoRecords}
                              alt="/"
                              height={90}
                              width={90}
                            />
                            <span className="text-base font-bold text-black dark:text-white">
                              {Strings.No_records_found}
                            </span>
                          </div>
                        </td>
                      </tr>
                    ) : (
                      filteredConnectedRows.map((connectedIndex, index) => {
                        console.log("Rendering Connected Row:", connectedIndex);
                        const connectedRow = TableData[connectedIndex];

                        if (connectedRow) {
                          console.log("Connected Row Data:", connectedRow);
                          return (
                            <tr
                              key={connectedIndex}
                              className="cursor-pointer border-b bg-white hover:bg-gray-50 dark:border-gray-700 dark:bg-[#1B1B1B] dark:hover:bg-dark-tundora"
                              onClick={handleReportModal}
                            >
                              <th
                                scope="row"
                                className="flex items-center whitespace-nowrap px-6 py-3 font-medium text-gray-900 dark:text-white"
                              >
                                {connectedRow.name}
                                <Image
                                  src={Images.Contacted}
                                  alt="/"
                                  height={14}
                                  width={14}
                                  className="ml-2"
                                />
                              </th>
                              <td className="px-6 py-3">
                                {connectedRow.testTaken}
                              </td>
                              <td className="px-6 py-3">
                                {connectedRow.createdAt
                                  ? new Date(
                                      connectedRow.createdAt
                                    ).toLocaleDateString("en-GB", {
                                      day: "2-digit",
                                      month: "2-digit",
                                      year: "numeric",
                                    })
                                  : "N/A"}
                              </td>
                              <td className="px-6 py-3">
                                {connectedRow.techStack.length > 0
                                  ? connectedRow.techStack.join(", ")
                                  : "N/A"}
                              </td>
                              <td className="px-6 py-3">
                                {connectedRow.softSkills || "N/A"}
                              </td>
                              <td className="px-6 py-3">
                                {connectedRow.proctoringResult || "N/A"}
                              </td>
                              <td className="px-6 py-3 text-right">
                                <a
                                  className=""
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    archiveRow(
                                      {
                                        ...connectedRow,
                                        // title: "",
                                        // location: "",
                                        // teston: "",
                                      },
                                      connectedIndex
                                    );
                                  }}
                                >
                                  <div className="z-50 cursor-pointer text-sm font-bold text-red-500">
                                    {Strings.Archive}
                                  </div>
                                </a>
                              </td>
                            </tr>
                          );
                        } else {
                          console.warn(
                            "Connected Row Data not found for index:",
                            connectedIndex
                          );
                          return null;
                        }
                      })
                    )}
                    <ReportModal
                      reportModal={reportModal}
                      setReportModal={setReportModal}
                    />
                  </tbody>
                </table>
              </div>
            </div>
          </Tab.Panel>
          <Tab.Panel>
            <div className="mt-5">
              <div className="relative- min-h-auto no-scrollbar mt-5 max-h-[420px] overflow-x-auto overflow-y-scroll shadow-md sm:rounded-lg">
                <table className="w-full  text-left text-sm text-gray-500 dark:text-gray-400 rtl:text-right">
                  <thead className="bg-opacity-[20%]- sticky top-0 bg-[#d7bfbf] text-sm text-gray-700 dark:bg-dark-CodGray dark:text-white">
                    <tr>
                      <th scope="col" className="px-6 py-3 dark:bg-[#863e41]">
                        {Strings.Name}
                      </th>
                      <th scope="col" className="px-6 py-3 dark:bg-[#863e41]">
                        {Strings.Test}
                      </th>
                      <th scope="col" className="px-6 py-3 dark:bg-[#863e41]">
                        {Strings.Date_taken}
                      </th>
                      <th scope="col" className="px-6 py-3 dark:bg-[#863e41]">
                        {Strings.Main_tech_stacks}
                      </th>
                      <th scope="col" className="px-6 py-3 dark:bg-[#863e41]">
                       {Strings.AI_Ratings}
                      </th>
                      <th scope="col" className="px-6 py-3 dark:bg-[#863e41]">
                        {Strings.Proctoring_result}
                      </th>
                      <th scope="col" className="px-6 py-3 dark:bg-[#863e41]">
                        <span className="sr-only">{Strings.Edit}</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {archivedData.length === 0 ? (
                      <tr className="text-center">
                        <td colSpan={7} className="py-8">
                          <div className="flex flex-col items-center justify-center">
                            <Image
                              src={Images.NoRecords}
                              alt="/"
                              height={90}
                              width={90}
                            />
                            <span className="text-base font-bold text-black dark:text-white">
                              {Strings.No_records_found}
                            </span>
                          </div>
                        </td>
                      </tr>
                    ) : (
                      archivedData.map((rowData, index) => (
                        <tr
                          key={index}
                          className="border-b bg-white hover:bg-gray-50 dark:border-gray-700 dark:bg-[#1B1B1B] dark:hover:bg-dark-tundora"
                        >
                          <th
                            scope="row"
                            className="whitespace-nowrap px-6 py-4 font-medium text-gray-900 dark:text-white"
                          >
                            {rowData.name}
                          </th>
                          <td className="px-6 py-4">{rowData.testTaken}</td>
                          <td className="px-6 py-4">
                            {rowData.createdAt
                              ? new Date(rowData.createdAt).toLocaleDateString(
                                  "en-GB",
                                  {
                                    day: "2-digit",
                                    month: "2-digit",
                                    year: "numeric",
                                  }
                                )
                              : "N/A"}
                          </td>
                          <td className="px-6 py-4">
                            {rowData.techStack.length > 0
                              ? rowData.techStack.join(", ")
                              : "N/A"}
                          </td>
                          <td className="px-6 py-4">
                            {rowData.softSkills || "N/A"}
                          </td>
                          <td className="px-6 py-4">
                            {rowData.proctoringResult || "N/A"}
                          </td>
                          <td className="px-6 py-4 text-right">
                            <a
                              className=""
                              onClick={() => handleUndo(rowData, index)}
                            >
                              <div className="z-50 cursor-pointer text-sm font-bold text-green-500">
                                {Strings.Unarchive}
                              </div>
                            </a>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </Tab.Panel>
        </Tab.Panels>
      </Tab.Group>
    </div>
  );
};

export async function getServerSideProps(context: any) {
  const token = context.req.cookies.token;

  if (!token) {
    return {
      redirect: {
        destination: "/auth/login",
        permanent: false,
      },
    };
  }

  return { props: {} };
}

export default gptvetting;

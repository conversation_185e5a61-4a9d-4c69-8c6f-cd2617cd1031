import { GetServerSideProps, InferGetServerSidePropsType } from "next";
import { Managing, UserData } from "@/interface";
import { useEffect, useState } from "react";

import Cookies from "js-cookie";
import { IRootState } from "@/store";
import Image from "next/image";
import { Images } from "@/constants";
import Loader from "@/components/Layouts/Loader";
import React from "react";
import { Strings } from "@/constants";
import axios from "axios";
import { useRouter } from "next/router";
import { useSelector } from "react-redux";

// const [recommandation, setrecommandation] = useState<{ recommended: UserData[], others: UserData[] }>({ recommended: [], others: [] });

export const getServerSideProps = (async (context) => {
  try {
    const token = context.req.cookies.token;
    const userId = context.req.cookies.userId;

    const commonHeaders = {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };

    const [managerDataResponse, hiredDataResponse, recommendationDataResponse] =
      await Promise.allSettled([
        axios.get(
          `${process.env.NEXT_PUBLIC_API_URL}devdashboard/getdeveloperManagerData?userId=${userId}`,
          commonHeaders
        ),
        axios.get(
          `${process.env.NEXT_PUBLIC_API_URL}myteam/getHiredData?userId=${userId}`,
          commonHeaders
        ),
        axios.get(
          `${process.env.NEXT_PUBLIC_API_URL}myteam/getRecommendationData?userId`,
          commonHeaders
        ),
      ]);

    const managerData =
      managerDataResponse?.status === "fulfilled"
        ? managerDataResponse?.value?.data?.managerData
        : [];
    const hiredData =
      hiredDataResponse?.status === "fulfilled"
        ? hiredDataResponse?.value?.data?.hiredData
        : [];
    const hiredDataCount = Array.isArray(hiredData) ? hiredData.length : 0;
    const userdataa =
      recommendationDataResponse?.status === "fulfilled"
        ? recommendationDataResponse?.value?.data?.recommendationData
        : [];

    return {
      props: {
        managerData,
        hiredDataCount,
        userdataa,
      },
    };
  } catch (error) {
    return {
      props: {},
    };
  }
}) as GetServerSideProps<{
  managerData: Managing | null;
  hiredDataCount: number;
  userdataa: UserData[];
}>;

const DashBoard = ({
  hiredDataCount,
  managerData,
  userdataa,
}: InferGetServerSidePropsType<typeof getServerSideProps>) => {
  const router = useRouter();
  const themeConfig = useSelector((state: IRootState) => state.themeConfig);

  const [recommandation, setrecommandation] = useState<{ recommended: UserData[], others: UserData[] }>({ recommended: [], others: [] });

  useEffect(() => {
    const fetchData = async () => {
      try {
        const token = Cookies.get("token");
        if (!token) {
          console.error("No token found in cookies");
          return;
        }

        const res = await axios.get(
          `${process.env.NEXT_PUBLIC_API_URL}myteam/getRecommendationData`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );

        console.log("API Response:", res.data);

        // Check if the response has the expected structure
        if (res.data && res.data.recommendationData) {
          const recommendationData = res.data.recommendationData;
          console.log("Recommendation Data:", recommendationData);

          // If recommendationData is an object with recommended/others properties
          if (recommendationData.recommended || recommendationData.others) {
            setrecommandation({
              recommended: recommendationData.recommended || [],
              others: recommendationData.others || []
            });
          }
          // If recommendationData is an array, treat it as recommended
          else if (Array.isArray(recommendationData)) {
            setrecommandation({
              recommended: recommendationData,
              others: []
            });
          }
          // If it's neither, set empty arrays
          else {
            console.warn("Unexpected recommendationData structure:", recommendationData);
            setrecommandation({ recommended: [], others: [] });
          }
        } else if (res.data && (res.data.recommended || res.data.others)) {
          // If the response directly has recommended/others structure
          setrecommandation({
            recommended: res.data.recommended || [],
            others: res.data.others || []
          });
        } else {
          // If the response is an array or different structure, handle it
          console.warn("Unexpected API response structure:", res.data);
          setrecommandation({ recommended: [], others: [] });
        }

        console.log("Recommendation data processed successfully");
      } catch (error) {
        console.error("Error fetching recommendation data:", error);
        // Set empty state on error to prevent undefined access
        setrecommandation({ recommended: [], others: [] });
      }
    };
    fetchData();
  }, []);


  return (
    <div>
      <div className="mb-4 flex items-center justify-between">
        <h1 className="select-none text-xl font-bold dark:text-white">
          {Strings.DASHBOARD}
        </h1>
        <a
          href="/hire-new-talent"
          className="nav-item group flex items-center rounded-[45px] bg-white px-7 py-2 text-black shadow-md hover:text-blue-500 dark:bg-[#000] dark:text-[#fff] ltr:pl-3 rtl:pr-3 "
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth="1.5"
            stroke="currentColor"
            className="h-6 w-6 text-[#8D3F42]"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M12 9v6m3-3H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>

          <text className="text-black hover:text-[#8D3F42] dark:text-white hover:dark:text-[#8D3F42] ltr:pl-3 rtl:pr-3">
            {Strings.HIRE_NEW_TALENT}
          </text>
        </a>
      </div>
      <div>
        <div className="flex w-full justify-between xs:flex-col md:flex-row md:gap-[20px]">
          <div className="mb-[20px] flex items-center justify-center space-x-2 rounded-lg border-none bg-white p-6 shadow outline-none dark:border-gray-700 dark:bg-[#000] xs:w-full lg:w-full xl:max-w-[720px] ">
            <div>
              <a href="/my-team">
                <button className="mb-[15px] text-[14px] font-bold dark:text-white">
                  {Strings.HIRED_ENGINEERS}
                </button>
                <button className="dark:text-white">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 -1 24 18"
                    strokeWidth="1.5"
                    stroke="currentColor"
                    className="h-4 w-4"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M8.25 4.5l7.5 7.5-7.5 7.5"
                    />
                  </svg>
                </button>
              </a>
              <p className="text-black-500 select-none text-center text-[20px] font-bold dark:text-white">
                {hiredDataCount}
              </p>
            </div>
          </div>
          <div className="dark:border-gray-700- mb-[20px] space-x-2 rounded-lg border-none bg-white p-6 shadow outline-none dark:bg-[#000] xs:w-full  lg:w-full  xl:max-w-[720px] ">
            <div>
              <div>
                <h2 className="dark:text-gray mb-[15px] select-none text-[14px] font-bold">
                  {Strings.YOUR_ACCOUNT_MANAGER}
                </h2>
              </div>
              {managerData ? (
                <>
                  <div className="flex w-full items-center gap-x-4">
                    <Image
                      src={
                        managerData.profilePicture ||
                        "/assets/images/favicon.png"
                      }
                      width={500}
                      height={500}
                      alt="manager"
                      className="h-20 w-20 rounded-full object-cover"
                    />
                    <div className="w-max">
                      <div>
                        <a href="#">
                          <h5 className="text-[14px] font-bold tracking-tight text-gray-900 dark:text-white">
                            {managerData.firstName || "Your"}{" "}
                            {managerData.lastName || "Manager"}
                          </h5>
                        </a>
                        <div className="flex flex-col sm:flex-col xl:flex-row">
                          <a
                            href={`mailto:${managerData.emailId}`}
                            className="mb-[5px] flex text-[16px] text-gray-500 dark:text-white"
                          >
                            {themeConfig.theme === "light" ? (
                              <img
                                className="mr-[5px] inline h-[20px] w-[20px]"
                                src={Images.MSG_WHITE}
                                alt="logo"
                              />
                            ) : (
                              <img
                                className="mr-[5px] inline h-[20px] w-[20px]"
                                src={Images.MSG}
                                alt="logo"
                              />
                            )}
                            {managerData.emailId}
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              fill="none"
                              viewBox="0 0 24 24"
                              strokeWidth="1.5"
                              stroke="currentColor"
                              className="ml-[3px] mt-[3px] h-3.5 w-3.5"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                d="M4.5 19.5l15-15m0 0H8.25m11.25 0v11.25"
                              />
                            </svg>
                          </a>
                          <strong className="mb-[5px] flex items-center px-[5px] dark:text-white xs:hidden sm:hidden lg:hidden xl:block">
                            |
                          </strong>
                          <a
                            href={`tel:${managerData.phoneNo}`}
                            className="inline-flex items-center text-[15px] hover:underline dark:text-white"
                          >
                            {themeConfig.theme === "light" ? (
                              <img
                                className="mr-[5px] inline h-[18px] w-[18px]"
                                src={Images.PHONE_WHITE}
                                alt="logo"
                              />
                            ) : (
                              <img
                                className="mr-[5px] inline h-[18px] w-[18px]"
                                src={Images.PHONE}
                                alt="logo"
                              />
                            )}
                            {managerData.phoneNo}
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              ) : (
                <div className="flex items-center justify-center pt-5">
                  <div className="flex flex-col items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      strokeWidth="1.5"
                      stroke="currentColor"
                      className="h-[50px] w-[50px]"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z"
                      />
                    </svg>
                    <h5 className="mb-4 text-xl font-medium">
                      {Strings.NOTHING_FOUND}
                    </h5>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-l select-none font-bold dark:text-white ">
          {Strings.HAND_PICKED_RECOMMENDATIONS}
        </h3>
      </div>
      <div className="mt-5">
        {recommandation.recommended && recommandation.recommended.length > 0 ? (
          recommandation.recommended.map((item, index) => {
            return (
              <button
                key={`recommended-${index}`}
                onClick={() =>
                  router.push(
                    {
                      pathname: "/apps/getRecommendation-details",
                      query: {
                        name: item.userData.firstName,
                        profile: item.userData.profilePicture,
                        country: item.userData.country,
                        position: item.userData.designation,
                        monthlySalary: item.userData.hourlyRate,
                        technicalInterviewNotes:
                          item.userData.technicalInterviewNotes,
                        softSkillAssessment: item.userData.softSkillAssessment,
                        otherTechnicalSkills: item.userData.techStack,
                        verifiedAiTools: item.userData.verifiedAiTools,
                        skill: item.vettingResults?.[0]?.skill,
                        skill_two: item.vettingResults?.[1]?.skill,
                        vettingResult: item.vettingResults?.[0].vettingResult,
                        vettingResult_two:
                          item.vettingResults?.[1].vettingResult,
                        yearOfExperience:
                          item.vettingResults?.[0].yearOfExperience,
                        yearOfExperience_two:
                          item.vettingResults?.[1].yearOfExperience,
                        monthlyPayment: item?.monthlyPayment,
                      },
                    },
                    "getRecommendation-details"
                  )
                }
                className="my-6 flex w-full items-center gap-x-4 rounded-xl bg-white px-4 py-3 shadow-md   dark:bg-[#000] dark:shadow-md "
              >
                <div className="bg-blue-300- p-2- rounded-full">
                  <Image
                    src={item.userData.profilePicture}
                    alt="profile"
                    width={500}
                    height={500}
                    className="h-28 w-28 rounded-full object-cover"
                  />
                </div>
                <div className="flex-1">
                  <div className=" flex flex-col items-start">
                    <div className="flex  w-full justify-between pr-3">
                      <div className="space-x-1- flex items-center">
                        <text className="text-base font-semibold leading-normal text-black dark:text-white">
                          {item.userData.firstName}
                        </text>
                      </div>
                    </div>
                    <div className=" px-2- py-0.5-">
                      <text className="text-sm  text-[#000] dark:text-white ">
                        {item.userData.designation}
                      </text>
                    </div>
                    <div className="flex w-full items-center justify-between">
                      {item.userData.country ? (
                        <div className="flex items-center">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            strokeWidth={1.5}
                            stroke="currentColor"
                            className="h-6 w-6"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              d="M15 10.5a3 3 0 11-6 0 3 3 0 016 0z"
                            />
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1115 0z"
                            />
                          </svg>
                          <text className="mx-1-">{item.userData.country}</text>
                        </div>
                      ) : (
                        <div></div>
                      )}

                      <div>
                        <text className="text-lg  font-bold text-black dark:text-white">
                          {item?.monthlyPayment != null &&
                            !isNaN(Number(item?.monthlyPayment)) ? (
                            <>
                              {Number(item?.monthlyPayment) >= 1000
                                ? `$${(
                                  Number(item?.monthlyPayment) / 1000
                                ).toFixed(1)}k`
                                : `$${item?.monthlyPayment}`}
                              /month
                            </>
                          ) : (
                            "Monthly payment not available"
                          )}
                        </text>
                      </div>
                    </div>
                  </div>
                </div>
              </button>
            );
          })
        ) : (
          <>
            <div className="  flex h-[35vh] w-full items-center justify-center">
              <div className="flex flex-col items-center">
                <img
                  src={Images.NOT_FOUND}
                  alt="Payment_logo"
                  className="h-[150px] w-[150px]"
                />
                <h1 className="text-xl font-bold leading-normal text-[#000] dark:text-white">
                  {Strings.NO_HANDPICKED}
                </h1>
                <p className="text-[18px] font-bold leading-normal text-[#000] dark:text-white">
                  {Strings.RECOMMENDATIONS}
                </p>
                <p className="text-[16px] font-bold text-[#000] dark:text-white">
                  {Strings.CREATE_YOUR_REQUIREMENT}
                </p>
                <p className="text-[16px] font-bold leading-normal text-[#000] dark:text-white">
                  {Strings.HAND_PICKED}
                </p>
                {/* <button className="nav-item grou- mt-[20px] flex items-center rounded-full bg-white  px-8 py-4 text-base shadow-md dark:bg-[#8d3f42]">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth="1.5"
                stroke="currentColor"
                className="h-6 w-6"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M12 9v6m3-3H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>

              <text className="text-black dark:text-[#fff] dark:group-hover:text-white-dark ltr:pl-3 rtl:pr-3">
                {Strings.Hire_New_Talent}
              </text>
            </button> */}
              </div>
            </div>
          </>
        )}
      </div>
      <div className="mt-10">
        <h3 className="text-l select-none font-bold dark:text-white ">
          {Strings.OTHER_ENGINEER}
        </h3>
      </div>
      <div className="mt-5">
        {recommandation.others && recommandation.others.length > 0 ? (
          recommandation.others.map((item, index) => {
            return (
              <button
                key={`others-${index}`}
                onClick={() =>
                  router.push({
                    pathname: "/apps/getRecommendation-details",
                    query: {
                      name: item.userData.firstName,
                      profile: item.userData.profilePicture,
                      country: item.userData.country,
                      position: item.userData.designation,
                      monthlySalary: item.userData.hourlyRate,
                      technicalInterviewNotes:
                        item.userData.technicalInterviewNotes,
                      softSkillAssessment: item.userData.softSkillAssessment,
                      otherTechnicalSkills: item.userData.techStack,
                      verifiedAiTools: item.userData.verifiedAiTools,
                      skill: item.vettingResults?.[0]?.skill,
                      skill_two: item.vettingResults?.[1]?.skill,
                      vettingResult: item.vettingResults?.[0].vettingResult,
                      vettingResult_two: item.vettingResults?.[1].vettingResult,
                      yearOfExperience: item.vettingResults?.[0].yearOfExperience,
                      yearOfExperience_two:
                        item.vettingResults?.[1].yearOfExperience,
                      monthlyPayment: item?.monthlyPayment,
                    },
                  }, "getRecommendation-details")
                }
                className="my-6 flex w-full items-center gap-x-4 rounded-xl bg-white px-4 py-3 shadow-md dark:bg-[#000] dark:shadow-md"
              >
                <div className="p-2">
                  <Image
                    src={item.userData.profilePicture}
                    alt="profile"
                    width={500}
                    height={500}
                    className="h-28 w-28 rounded-full object-cover"
                  />
                </div>
                <div className="flex-1">
                  <div className="flex flex-col items-start">
                    <div className="flex w-full justify-between pr-3">
                      <div className="flex items-center">
                        <text className="text-base font-semibold leading-normal text-black dark:text-white">
                          {item.userData.firstName}
                        </text>
                      </div>
                    </div>
                    <div>
                      <text className="text-sm text-[#000] dark:text-white">
                        {item.userData.designation}
                      </text>
                    </div>
                    <div className="flex w-full items-center justify-between">
                      {item.userData.country && (
                        <div className="flex items-center">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            strokeWidth={1.5}
                            stroke="currentColor"
                            className="h-6 w-6"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              d="M15 10.5a3 3 0 11-6 0 3 3 0 016 0z"
                            />
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1115 0z"
                            />
                          </svg>
                          <text className="mx-1">{item.userData.country}</text>
                        </div>
                      )}
                      <div>
                        <text className="text-lg font-bold text-black dark:text-white">
                          {item?.monthlyPayment != null &&
                            !isNaN(Number(item?.monthlyPayment)) ? (
                            <>
                              {Number(item?.monthlyPayment) >= 1000
                                ? `$${(Number(item?.monthlyPayment) / 1000).toFixed(1)}k`
                                : `$${item?.monthlyPayment}`}
                              /month
                            </>
                          ) : (
                            "Monthly payment not available"
                          )}
                        </text>
                      </div>
                    </div>
                  </div>
                </div>
              </button>
            );
          })
        ) : (
          <p className="text-center text-sm font-medium text-gray-500 dark:text-white">
            No other engineers found.
          </p>
        )}
      </div>

    </div>
  );
};

export default DashBoard;
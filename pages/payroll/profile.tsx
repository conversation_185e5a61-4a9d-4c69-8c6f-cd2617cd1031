import AboutSection from "@/components/AboutSection";
import BackButton from "@/components/BackButton";
import ExperienceSection from "@/components/ExperienceSection";
import ProfileInfoCard from "@/components/ProfileInfoCard";
import { IUser } from "@/interface";
import { getUserProfile } from "@/service/searchTalent.service";
import { GetServerSideProps, InferGetServerSidePropsType } from "next";
import { useState } from "react";

export const getServerSideProps = (async ({ query, req }) => {
  const token = req.cookies.token;
  try {
    const response = await getUserProfile(query?.userId as string, token);
    if (response.error) {
      return {
        props: {},
      };
    }
    return {
      props: {
        data: response.data,
      },
    };
  } catch (error) {
    return {
      props: {},
    };
  }
}) as GetServerSideProps<{
  data: IUser;
}>;
const Profile = ({
  data,
}: InferGetServerSidePropsType<typeof getServerSideProps>) => {
  const [currentTab, setCurrentTab] = useState(0);
  const tabs = [
    {
      id: 0,
      name: `About ${data.firstName.charAt(0).toUpperCase() + "."} ${
        data.lastName.charAt(0).toUpperCase() + "."
      }`,
      Component: <AboutSection data={data} />,
    },
    {
      id: 1,
      name: "Experience",
      Component: <ExperienceSection data={data} />,
    },
  ];

  return (
    <>
      <BackButton />
      <ProfileInfoCard
        profilePicture={data.profilePicture}
        email={data.emailId}
        linkedInUrl={data.socialLinkId}
        location={data.country}
        firstName={data.firstName.charAt(0).toUpperCase() + "."}
        lastName={data.lastName.charAt(0).toUpperCase() + "."}
        // lastName={data.lastName}
        phone={`${data.phoneNo}`}
        title={data.designation}
        interviewDate={data.hiredDate as string}
        i18nIsDynamicList
      />

      <section className="mx-auto mt-5">
        <nav className="mb-8 flex gap-4 border-b">
          {tabs.map((tab, index) => (
            <button
              key={tab.id}
              className={
                currentTab === index
                  ? `bg-red-900- rounded-t-[5px] border-b-2 border-dark-tosca bg-dark-tosca bg-opacity-[.25] p-2 text-black outline-none transition-colors`
                  : "transition-colors"
              }
              onClick={() => setCurrentTab(index)}
            >
              {tab.name}
            </button>
          ))}
        </nav>

        {tabs[currentTab].Component}
      </section>
    </>
  );
};

export default Profile;

import Pagination from "@/components/pagination";
import PopupDropdown from "@/components/PopupDropdown";
import Switch from "@/components/Switch";
import { IUser } from "@/interface";
import searchIcon from "@/public/assets/svg/searchIcon.svg";
import {
  ITalentQuery,
  requestForInterview,
  searchTalent,
} from "@/service/searchTalent.service";
import _ from "lodash";
import Image from "next/image";
import { useCallback, useEffect, useRef, useState } from "react";
import Swal from "sweetalert2";
import Filtermodal from "../components/filter-popup";
import Loader from "../components/Layouts/Loader";
import { Images, Strings } from "../constants";
import { useRouter } from "next/router";
import FullScreenLoader from "@/components/Layouts/FullScreenLoader";
import useLocalStorage from "@/hooks/useLocalStorage";
import BackButton from "@/components/BackButton";

export async function getServerSideProps(context: any) {
  const token = context.req.cookies.token;

  if (!token) {
    return {
      redirect: {
        destination: "/auth/login",
        permanent: false,
      },
    };
  }

  return { props: {} };
}
const SearchTalent = () => {
  const isMounted = useRef(false);
  const router = useRouter();
  const PAGE_LIMIT = 10;
  const sortRef = useRef<HTMLDivElement>(null);
  const [loading, setLoading] = useState(true);
  const [isOpen, setIsOpen] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [talentList, setTalentList] = useState<IUser[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [reFetch, setReFetch] = useState(false);
  const [query, setQuery] = useState<ITalentQuery>({
    search: "",
    requestForInterview: false,
    page: 1,
    limit: PAGE_LIMIT,
    sortBy: "default",
  });

  const advancedFilterInitial = {
    technicalSkills: [],
    softSkills: [],
    country: [],
    pricePerHour: 0,
    availability: "",
  };
  const [advancedFilter, setAdvancedFilter] = useLocalStorage(
    "advancedFilter",
    advancedFilterInitial
  );

  const modalOpen = () => setIsModalOpen(true);
  const modalClose = () => setIsModalOpen(false);
  const openModal = () => setIsOpen(true);
  const closeModal = () => setIsOpen(false);

  const sortOptions = [
    {
      label: "Default",
      value: "default",
    },
    {
      label: "Rate: Low - High",
      value: "rateLowToHigh",
    },
    {
      label: "Rate: High - Low",
      value: "rateHighToLow",
    },
    {
      label: "Interview Requested",
      value: "requestForInterview",
    },
  ];

  useEffect(() => {
    const handleClick = (e: MouseEvent) => {
      if (sortRef.current && !sortRef.current.contains(e.target as Node)) {
        setIsModalOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClick);
    return () => {
      document.removeEventListener("mousedown", handleClick);
    };
  }, []);

  const findLabel = (value: string) => {
    return sortOptions.find((option) => option.value === value)?.label;
  };

  const handleQuery = (key: string, value: string | boolean | number) => {
    setQuery({
      ...query,
      [key]: value,
      ...(key !== "page" && { page: 1 }),
    });
  };

  const fetchData = async (
    searchQuery: ITalentQuery,
    advancedFilter: typeof advancedFilterInitial
  ) => {
    try {
      setLoading(true);
      const response = await searchTalent({
        ...searchQuery,
        ...advancedFilter,
      });
      if (response.error) {
        Swal.fire({
          toast: true,
          position: "top-end",
          showConfirmButton: false,
          timer: 3000,
          icon: "error",
          title: "Error fetching data",
          padding: "10px 20px",
          customClass: {
            popup: "swal-custom-toast",
          },
        });
        return;
      }
      if (response.data?.data) {
        setTalentList(response.data.data);
        setTotalCount(response.data.total);
      }
    } catch (error) {
      Swal.fire({
        toast: true,
        position: "top-end",
        showConfirmButton: false,
        timer: 3000,
        icon: "error",
        title: "Error fetching data",
        padding: "10px 20px",
        customClass: {
          popup: "swal-custom-toast",
        },
      });
    } finally {
      setLoading(false);
      isMounted.current = true;
    }
  };

  const debouncedFetchData = useCallback(
    _.debounce(
      (searchQuery, advancedFilter) => fetchData(searchQuery, advancedFilter),
      300
    ),
    []
  );

  useEffect(() => {
    debouncedFetchData(query, advancedFilter);
    return () => {
      debouncedFetchData.cancel();
    };
  }, [query, debouncedFetchData, reFetch]);

  const handleRequestForInterview = async (userId: string) => {
    try {
      const userDataJson = await localStorage.getItem("userData");
      if (!userDataJson) {
        return;
      }
      const userData = JSON.parse(userDataJson);
      const clientId = userData.id;
      const response = await requestForInterview(userId, clientId);
      if (response.error) {
        Swal.fire({
          toast: true,
          position: "top-end",
          showConfirmButton: false,
          timer: 3000,
          icon: "error",
          title: "Error requesting interview",
          padding: "10px 20px",
          customClass: {
            popup: "swal-custom-toast",
          },
        });
        return;
      }

      Swal.fire({
        toast: true,
        position: "top-end",
        showConfirmButton: false,
        timer: 3000,
        icon: "success",
        title: "Interview requested successfully",
        padding: "10px 20px",
        customClass: {
          popup: "swal-custom-toast",
        },
      });

      setReFetch(!reFetch);
    } catch (error) {
      Swal.fire({
        toast: true,
        position: "top-end",
        showConfirmButton: false,
        timer: 3000,
        icon: "error",
        title: "Error requesting interview",
        padding: "10px 20px",
        customClass: {
          popup: "swal-custom-toast",
        },
      });
    }
  };

  const handleApplyFilter = () => {
    setReFetch(!reFetch);
  };

  const appliedFilters = () => {
    let filters = [];
    if (advancedFilter.technicalSkills.length) {
      filters.push({
        title: "Technical Skills",
        values: advancedFilter.technicalSkills,
      });
    }
    if (advancedFilter.softSkills.length) {
      filters.push({
        title: "Soft Skills",
        values: advancedFilter.softSkills,
      });
    }

    if (advancedFilter.country.length) {
      filters.push({
        title: "Country",
        values: advancedFilter.country,
      });
    }

    if (advancedFilter.pricePerHour !== 0) {
      filters.push({
        title: "Price Per Hour",
        values: advancedFilter.pricePerHour,
      });
    }

    if (advancedFilter.availability) {
      filters.push({
        title: "Availability",
        values: advancedFilter.availability,
      });
    }
    return filters;
  };

  const handleOpenProfile = (userId: string) => {
    router.push({
      pathname: "/payroll/profile",
      query: { userId: userId?.toLocaleLowerCase() },
    });
  };

  if (!isMounted.current) {
    return <FullScreenLoader />;
  }

  return (
    <div>
      <BackButton />
      <div className=" flex w-full items-center justify-between xs:flex-col md:flex-row">
        <div className="xs:mb-2px w-[100%]">
          <h1 className="text-[26px] font-bold leading-normal text-black dark:text-white xs:text-center md:text-start">
            {Strings.SEARCH_TALENT}
          </h1>
          <p className="text-[13px] leading-normal text-black dark:text-white xs:text-center md:text-start">
            {Strings.PARAGRAPH_SEARCH}
          </p>
        </div>
      </div>
      <div className="x-1 y-0.5 my-5 flex w-full justify-between rounded  bg-white p-[15px] dark:bg-[#000] xs:flex-col xs:items-center md:flex-row">
        <div className="flex xs:flex-col xl:flex-row">
          <div className="color-gray-500 mr-2 flex h-[40px] rounded-[10px] border bg-transparent xs:w-full xs:items-center xl:w-[200px] ">
            <Image
              src={searchIcon}
              alt="search icon"
              className="ml-[0.63rem] mr-2 h-6 w-6"
            />
            <input
              className="w-full border-none bg-transparent outline-none "
              type="text"
              placeholder="Search by name,skills"
              onChange={(e) => handleQuery("search", e.target.value)}
            />
          </div>
        </div>
        <div className="w-[50%]- bg-yellow-900- flex ">
          <div
            onClick={openModal}
            className="ml-[10px] flex cursor-pointer  items-center rounded-[10px] border bg-transparent p-3 text-white transition duration-300  ease-in-out dark:shadow xs:ml-[0] xs:text-sm"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth="1.5"
              stroke="currentColor"
              className="h-4 w-4 text-[#8D3F42]"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M19.5 8.25l-7.5 7.5-7.5-7.5"
              />
            </svg>
            <button className="pl-[5px] text-[16px] leading-normal text-black dark:text-white xs:text-[10px]">
              {appliedFilters().length > 0 ? (
                <div className="relative flex items-center gap-x-2">
                  <span>{appliedFilters().length} Filters Applied</span>
                  <button
                    type="button"
                    className="absolute -right-5 -top-5 rounded-full bg-[#8D3F42] p-0.5 text-white"
                    title="Clear Filters"
                    onClick={(event) => {
                      event.stopPropagation();
                      setAdvancedFilter(advancedFilterInitial);
                      setReFetch(!reFetch);
                    }}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      strokeWidth="1.5"
                      stroke="currentColor"
                      className="h-3 w-3 text-white"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </button>
                </div>
              ) : (
                Strings.FILTERS
              )}
            </button>
          </div>
          <div>
            <Filtermodal
              isOpen={isOpen}
              closeModal={closeModal}
              advancedFilter={advancedFilter}
              setAdvancedFilter={setAdvancedFilter}
              handleApplyFilter={handleApplyFilter}
            />
          </div>
          <button
            className=" ml-[10px] flex items-center  rounded-[10px] border bg-transparent p-3 text-white transition duration-300 ease-in-out dark:shadow xs:px-2  xs:py-1 xs:text-sm"
            onClick={modalOpen}
          >
            <img
              src={Images.BARS_FILTER}
              alt="Filter icon"
              className="w-[20px]  text-white"
            />

            <h3 className=" px-[10px] text-[16px] leading-normal text-black dark:text-white xs:text-[10px]">
              {findLabel(query.sortBy)}
            </h3>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth="1.5"
              stroke="currentColor"
              className="h-4 w-4"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M19.5 8.25l-7.5 7.5-7.5-7.5"
              />
            </svg>
          </button>

          {isModalOpen && (
            <div
              className="mobile:w-full absolute ml-[65px] mt-[55px] w-[180px] rounded-lg border border-[#D3D2E2] 
        bg-white p-[8px] shadow-[0_4px_10px_0px_rgba(0,0,0,0.12)]"
              ref={sortRef}
            >
              <ul>
                {sortOptions.map((option, index) => (
                  <li
                    key={index}
                    className="w-full cursor-pointer p-[10px] font-medium hover:rounded-[6px]
               hover:bg-[#F5F4F8]"
                    onClick={() => {
                      handleQuery("sortBy", option.value);
                      modalClose();
                    }}
                  >
                    {option.label}
                  </li>
                ))}
              </ul>
            </div>
          )}

          <div className=" ml-[10px] flex items-center  rounded-[10px] border bg-transparent p-3  text-white transition duration-300 ease-in-out dark:shadow  xs:py-1 xs:text-[14px]">
            <button className="text-[16px] leading-normal text-black dark:text-white xs:text-[10px]">
              {Strings.INTERVIEW_REQUEST}
            </button>
            <Switch handleQuery={handleQuery} field="requestForInterview" />
          </div>
        </div>
      </div>
      {!loading && talentList.length ? (
        <div>
          <p>{` ${totalCount} results found`}</p>
        </div>
      ) : null}
      {loading && isMounted.current ? <Loader /> : null}
      {!loading ? (
        talentList.length === 0 ? (
          <p>{Strings.NO_RESULTS_FOUND}</p>
        ) : (
          talentList.map((item) => {
            return (
              <>
                <button
                  className="my-2 flex w-full flex-col items-center justify-center rounded-xl bg-white px-4 py-3 shadow-md outline-none dark:bg-[#000] dark:shadow-md"
                  onClick={() => {
                    handleOpenProfile(item.userId);
                  }}
                  title="Open Profile"
                >
                  <div className="flex w-full items-center justify-between">
                    <div className="flex items-center justify-center gap-x-3">
                      <div className="rounded-full">
                        <Image
                          src={
                            item?.profilePicture
                              ? item?.profilePicture
                              : `https://avatar.iran.liara.run/username?username=${item?.firstName}+${item?.lastName}`
                          }
                          alt="profile"
                          height={400}
                          width={400}
                          className="h-28 w-28 rounded-full object-cover"
                        />
                      </div>
                      <div className="pl-2.5">
                        <div className=" flex flex-col items-start">
                          <div className="flex  w-full justify-between pr-3">
                            <div className="flex w-full items-center justify-between space-x-1 md:flex-row xl:flex-row">
                              <div className="flex items-center space-x-2 text-base font-semibold text-black dark:text-white">
                                <p>{`${item.firstName} ${item.lastName}`}</p>
                                {item?.interviewRequest?.requestForInterview ===
                                  true && (
                                  <button className="rounded-lg bg-green-200 px-2 py-1 text-xs text-black dark:text-white">
                                    Requested for Interview
                                  </button>
                                )}
                              </div>
                              <div className="flex"></div>
                            </div>
                          </div>
                          <div className="mb-[5px]">
                            <text className="text-[14px] leading-normal text-black dark:text-white">
                              {item.designation}
                            </text>
                          </div>
                          <div className="xs:flex-start  flex w-full justify-between xs:flex-col md:flex-row md:items-center">
                            <div className="flex items-center">
                              {item.techStack.map((skill, index) => (
                                <button
                                  key={index}
                                  className=" mr-1 flex items-center justify-center rounded-full  border-none bg-[#8D3F42] px-2 py-1.5 text-center text-white transition duration-300 ease-in-out dark:shadow xs:text-sm"
                                >
                                  {skill}
                                </button>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="flex flex-col items-end space-y-3">
                      <PopupDropdown
                        items={[
                          ...(item?.interviewRequest?.requestForInterview !==
                          true
                            ? [
                                {
                                  title: `Request Interview`,
                                  onClick: () =>
                                    handleRequestForInterview(item.id),
                                },
                              ]
                            : []),
                          {
                            title: `View Profile`,
                            onClick: () => handleOpenProfile(item.userId),
                          },
                        ]}
                      />
                      {item.hourlyRate && (
                        <button className="mt-[10px] flex items-center rounded-xl border border-black px-2 py-1.5 shadow-sm dark:shadow md:mt-[1px]">
                          <text>{`$${item.hourlyRate}/hour`}</text>
                        </button>
                      )}
                    </div>
                  </div>
                </button>
              </>
            );
          })
        )
      ) : <Loader />}
      {totalCount > 0 && (
        <Pagination
          currentPage={query.page - 1}
          handleChangePage={(page) => handleQuery("page", page + 1)}
          pageCount={totalCount > 0 ? Math.ceil(totalCount / PAGE_LIMIT) : 0}
        />
      )}
    </div>
  );
};

export default SearchTalent;

import React, { useEffect } from "react";

import BlankLayout from "@/components/Layouts/BlankLayout";
import Technicalform from "@/components/Technical-form";

const TechnicalVetting = () => {
  useEffect(() => {
    const disableContextMenu = (e: MouseEvent) => e.preventDefault();
    document.addEventListener("contextmenu", disableContextMenu);

    const handleKeyDown = (e: KeyboardEvent) => {
      if (
        e.key === "F12" ||
        (e.ctrlKey &&
          e.shiftKey &&
          (e.key === "I" || e.key === "J" || e.key === "C")) ||
        (e.ctrlKey && e.key === "U") ||
        (e.metaKey &&
          e.altKey &&
          (e.key === "I" || e.key === "J" || e.key === "C")) ||
        (e.metaKey && e.key === "U")
      ) {
        e.preventDefault();
      }
    };

    document.addEventListener("keydown", handleKeyDown);

    return () => {
      document.removeEventListener("contextmenu", disableContextMenu);
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, []);

  return (
    <div className="force-light-mode m-auto max-w-screen-2xl">
      <Technicalform />
    </div>
  );
};

export async function getServerSideProps(context: any) {
  const token = context.req.cookies.token;

  if (!token) {
    return {
      redirect: {
        destination: "/auth/login",
        permanent: false,
      },
    };
  }

  return { props: {} };
}

TechnicalVetting.getLayout = (page: any) => {
  return <BlankLayout forceLightMode>{page}</BlankLayout>;
};

export default TechnicalVetting;

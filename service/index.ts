import axios, { AxiosInstance, AxiosRequestConfig } from "axios";
import Cookies from "js-cookie";

export const Api = (token?: string): AxiosInstance => {
  const options: AxiosRequestConfig = {
    baseURL: process.env.NEXT_PUBLIC_API_URL,
    headers: {
      "Content-Type": "application/json",
    },
  };
  const instance = axios.create(options);

  instance.interceptors.request.use(
    async (config) => {
      const accessToken = token ?? Cookies.get("token");
      if (!token) return config;
      config.headers.Authorization = `Bearer ${accessToken}`;
      return config;
    },
    (error) => {
      console.error(error);
      return Promise.reject(error);
    }
  );

  return instance;
};

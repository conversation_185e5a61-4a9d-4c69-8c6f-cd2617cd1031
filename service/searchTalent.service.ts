import { Api } from ".";
import { IUser } from "@/interface";
import _ from "lodash";
import { isFalsyOrEmpty } from "@/utils";

export interface ITalentQuery {
  search: string;
  requestForInterview: boolean;
  page: number;
  limit: number;
  sortBy: string;
}

export interface ITalentResponse {
  error: boolean;
  message: any;
  data: {
    total: number;
    data: IUser[];
  } | null;
}

type Skill = {
  skill: string;
  level: string;
};

export interface IAdvancedFilter {
  technicalSkills: string[];
  softSkills: string[];
  country: string[];
  pricePerHour: number;
  availability: string;
}

export const searchTalent = async (
  query: ITalentQuery & IAdvancedFilter
): Promise<ITalentResponse> => {
  try {
    const { search, requestForInterview, limit, page, sortBy } = query;
    const response = await Api().get("user/searchTalent", {
      params: {
        ...(requestForInterview && { requestForInterview }),
        ...(!isFalsyOrEmpty(search) && { search }),
        ...(!isFalsyOrEmpty(sortBy) && { sortBy }),
        page,
        limit,
        ...(!isFalsyOrEmpty(query.technicalSkills) && {
          technicalSkills: JSON.stringify(query.technicalSkills),
        }),
        ...(!isFalsyOrEmpty(query.softSkills) && {
          softSkills: JSON.stringify(query.softSkills),
        }),
        ...(!isFalsyOrEmpty(query.country) && {
          country: JSON.stringify(query.country),
        }),
        pricePerHour: query.pricePerHour,
        ...(!isFalsyOrEmpty(query.availability) && {
          availability: query.availability,
        }),
      },
    });
    return {
      error: false,
      message: "Success",
      data: response.data?.searchTalentData,
    };
  } catch (error) {
    return {
      error: true,
      message: "Error",
      data: null,
    };
  }
};

export const requestForInterview = async (userId: string, clientId: string) => {
  try {
    const response = await Api().put("user/requestInterview", {
      userId,
      clientId,
    });
    return {
      error: false,
      message: "Success",
      data: response.data,
    };
  } catch (error) {
    return {
      error: true,
      message: "Error",
      data: null,
    };
  }
};

export const getUserProfile = async (userId: string, token?: string) => {
  try {
    const response = await Api(token).get(`user/getUserData`, {
      params: { userId: userId?.toUpperCase() },
    });

    return {
      error: false,
      message: "Success",
      data: response.data?.userData,
    };
  } catch (error) {
    return {
      error: true,
      message: "Error",
      data: null,
    };
  }
};

export const inviteCandidate = async (
  emailId: string,
  name: string,
  skills?: Skill[],
  isProctoring?: boolean,
  isCodingExcercise?: boolean
) => {
  try {
    console.log("Inviting candidate with skills:", skills);
    const response = await Api().post("gptVetting/sendEmail", {
      emailId, name, ...(skills && { skills }), isProctoring, isCodingExcercise
    });
    return {
      error: false,
      message: "Success",
      data: response.data,
    };
  } catch (error) {
    return {
      error: true,
      message: "Error",
      data: null,
    };
  }
};

export const inviteCandidateCsv = async (
  file: File
) => {
  try {
    const formData = new FormData();
    formData.append("file", file, file.name);

    const response = await Api().post("gptVetting/uploadCSVCandidate", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });

    return {
      error: false,
      message: "Success",
      data: response.data,
    };
  } catch (error) {
    return {
      error: true,
      message: "Error",
      data: null,
    };
  }
}
// emailSlice.ts
import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface EmailState {
  emailId: string;
}

const initialState: EmailState = {
  emailId: "",
};

const emailSlice = createSlice({
  name: "email",
  initialState,
  reducers: {
    setEmailId: (state, action: PayloadAction<string>) => {
      state.emailId = action.payload;
    },
    clearEmailId: (state) => {
      state.emailId = "";
    },
  },
});

export const { setEmailId, clearEmailId } = emailSlice.actions;
export default emailSlice.reducer;
